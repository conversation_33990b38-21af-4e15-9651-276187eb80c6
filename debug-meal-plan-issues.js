// Debug script for meal plan issues
// Run this in browser console to check for common issues

console.log('🔍 Starting Meal Plan Debug Check...');

// Check 1: Verify apiService is loaded and has saveMealPlan
try {
    if (typeof window !== 'undefined' && window.apiService) {
        console.log('✅ apiService found in window');
        if (window.apiService.saveMealPlan) {
            console.log('✅ saveMealPlan function exists in apiService');
        } else {
            console.error('❌ saveMealPlan function NOT found in apiService');
            console.log('Available functions:', Object.keys(window.apiService));
        }
    } else {
        console.log('⚠️ apiService not found in window (this is normal for React apps)');
    }
} catch (error) {
    console.error('❌ Error checking apiService:', error);
}

// Check 2: Test if we can import apiService (for React apps)
async function checkApiServiceImport() {
    try {
        // This would work in a React component context
        console.log('🔄 Attempting to check apiService import...');
        
        // Check if the file exists
        const response = await fetch('/src/services/apiService.js');
        if (response.ok) {
            const content = await response.text();
            
            if (content.includes('saveMealPlan')) {
                console.log('✅ saveMealPlan found in apiService.js file');
                
                // Check if it's exported
                if (content.includes('saveMealPlan,') || content.includes('saveMealPlan:')) {
                    console.log('✅ saveMealPlan appears to be exported');
                } else {
                    console.error('❌ saveMealPlan may not be properly exported');
                }
            } else {
                console.error('❌ saveMealPlan NOT found in apiService.js file');
            }
        } else {
            console.log('⚠️ Could not fetch apiService.js (normal if not in dev server context)');
        }
    } catch (error) {
        console.log('⚠️ Could not check apiService file:', error.message);
    }
}

// Check 3: Test backend connectivity
async function checkBackendConnection() {
    console.log('🔄 Testing backend connection...');
    
    const API_BASE_URL = 'http://localhost:5000/api';
    
    try {
        const response = await fetch(`${API_BASE_URL}/meal-plans`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token') || 'test'}`
            }
        });
        
        if (response.ok) {
            console.log('✅ Backend is responding');
        } else {
            console.log(`⚠️ Backend responded with status: ${response.status}`);
        }
    } catch (error) {
        console.error('❌ Backend connection failed:', error.message);
        console.log('💡 Make sure backend is running on localhost:5000');
    }
}

// Check 4: Test save endpoint specifically
async function checkSaveEndpoint() {
    console.log('🔄 Testing save meal plan endpoint...');
    
    const API_BASE_URL = 'http://localhost:5000/api';
    const testData = {
        name: 'Debug Test Plan',
        startDate: '2025-08-28',
        endDate: '2025-08-28',
        meals: {}
    };
    
    try {
        const response = await fetch(`${API_BASE_URL}/meal-plans/save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token') || 'test'}`
            },
            body: JSON.stringify(testData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ Save endpoint is working');
            console.log('Response:', data);
        } else {
            console.log(`⚠️ Save endpoint responded with status: ${response.status}`);
            console.log('Response:', data);
        }
    } catch (error) {
        console.error('❌ Save endpoint test failed:', error.message);
    }
}

// Check 5: Look for common React errors
function checkReactErrors() {
    console.log('🔄 Checking for common React/import errors...');
    
    // Check if there are any unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
        console.error('❌ Unhandled Promise Rejection:', event.reason);
    });
    
    // Check for module import errors
    window.addEventListener('error', function(event) {
        if (event.message && event.message.includes('import')) {
            console.error('❌ Import Error:', event.message);
        }
    });
}

// Run all checks
async function runAllChecks() {
    console.log('🚀 Running all debug checks...');
    
    checkReactErrors();
    await checkApiServiceImport();
    await checkBackendConnection();
    await checkSaveEndpoint();
    
    console.log('🏁 Debug check complete!');
    console.log('📋 Summary:');
    console.log('1. Check console above for any ❌ errors');
    console.log('2. If you see errors, the fixes may need adjustment');
    console.log('3. If you see mostly ✅, the fixes should be working');
}

// Auto-run checks
runAllChecks();

// Export functions for manual testing
window.debugMealPlan = {
    checkApiServiceImport,
    checkBackendConnection,
    checkSaveEndpoint,
    runAllChecks
};

console.log('💡 You can also run individual checks:');
console.log('- debugMealPlan.checkApiServiceImport()');
console.log('- debugMealPlan.checkBackendConnection()');
console.log('- debugMealPlan.checkSaveEndpoint()');
