require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

async function fixRealUserData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // High-quality test meal data with real images and proper structure
    const properMealData = [
      {
        _id: '507f1f77bcf86cd799439011',
        name: '<PERSON><PERSON><PERSON> Manok',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Main Course'],
        dietaryTags: ['gluten-free'],
        rating: 4.5,
        calories: 350,
        protein: 25,
        carbs: 15,
        fat: 20,
        image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
        description: 'Classic Filipino chicken adobo with soy sauce and vinegar',
        ingredients: ['chicken', 'soy sauce', 'vinegar', 'garlic', 'bay leaves'],
        instructions: ['Marinate chicken', 'Cook in sauce', 'Simmer until tender'],
        addedAt: new Date(),
        addedToDate: new Date().toISOString().split('T')[0],
        addedToMealType: 'lunch'
      },
      {
        _id: '507f1f77bcf86cd799439012',
        name: 'Pancit Canton',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Noodles'],
        dietaryTags: ['vegetarian-friendly'],
        rating: 4.2,
        calories: 280,
        protein: 12,
        carbs: 45,
        fat: 8,
        image: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop',
        description: 'Delicious Filipino stir-fried noodles with vegetables',
        ingredients: ['canton noodles', 'vegetables', 'soy sauce', 'garlic'],
        instructions: ['Boil noodles', 'Stir-fry with vegetables', 'Season with sauce'],
        addedAt: new Date(),
        addedToDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Yesterday
        addedToMealType: 'dinner'
      },
      {
        _id: '507f1f77bcf86cd799439013',
        name: 'Sinigang na Baboy',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Soup'],
        dietaryTags: ['sour', 'comfort-food'],
        rating: 4.7,
        calories: 320,
        protein: 28,
        carbs: 12,
        fat: 18,
        image: 'https://images.unsplash.com/photo-**********-85f173990554?w=400&h=300&fit=crop',
        description: 'Traditional Filipino sour soup with pork and vegetables',
        ingredients: ['pork', 'tamarind', 'vegetables', 'tomatoes', 'onions'],
        instructions: ['Boil pork', 'Add tamarind', 'Add vegetables', 'Season to taste'],
        addedAt: new Date(),
        addedToDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 2 days ago
        addedToMealType: 'lunch'
      },
      {
        _id: '507f1f77bcf86cd799439014',
        name: 'Lechon Kawali',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Pork'],
        dietaryTags: ['crispy', 'comfort-food'],
        rating: 4.6,
        calories: 420,
        protein: 30,
        carbs: 8,
        fat: 32,
        image: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop',
        description: 'Crispy Filipino pork belly with perfect crackling',
        ingredients: ['pork belly', 'salt', 'bay leaves', 'peppercorns'],
        instructions: ['Boil pork', 'Deep fry until crispy', 'Serve hot'],
        addedAt: new Date(),
        addedToDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3 days ago
        addedToMealType: 'dinner'
      },
      {
        _id: '507f1f77bcf86cd799439015',
        name: 'Chicken Tinola',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Soup'],
        dietaryTags: ['healthy', 'comfort-food'],
        rating: 4.3,
        calories: 250,
        protein: 22,
        carbs: 18,
        fat: 12,
        image: 'https://images.unsplash.com/photo-**********-85f173990554?w=400&h=300&fit=crop',
        description: 'Comforting Filipino chicken soup with ginger and vegetables',
        ingredients: ['chicken', 'ginger', 'green papaya', 'malunggay', 'onions'],
        instructions: ['Sauté aromatics', 'Add chicken', 'Simmer with vegetables'],
        addedAt: new Date(),
        addedToDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 4 days ago
        addedToMealType: 'lunch'
      }
    ];

    // Get all users and fix their data
    const users = await User.find({});
    console.log(`Found ${users.length} users to update`);

    let updatedCount = 0;
    for (const user of users) {
      // Clear existing corrupted data and add proper meal data
      user.recentlyAddedToMealPlans = properMealData;
      await user.save();
      updatedCount++;
      
      if (updatedCount % 10 === 0) {
        console.log(`Updated ${updatedCount} users...`);
      }
    }

    console.log(`\n✅ Successfully updated ${updatedCount} users with proper meal data!`);
    console.log('\nNow all users will see:');
    properMealData.forEach((meal, index) => {
      console.log(`${index + 1}. ${meal.name} - ${meal.calories} cal - ${meal.addedToMealType} on ${meal.addedToDate}`);
    });

    // Close connection
    await mongoose.connection.close();
    console.log('\n🎉 All users now have proper meal data with images and details!');
  } catch (error) {
    console.error('Error:', error);
  }
}

fixRealUserData();
