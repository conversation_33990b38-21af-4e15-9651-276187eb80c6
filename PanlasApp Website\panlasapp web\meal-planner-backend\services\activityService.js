const Activity = require('../models/Activity');
const User = require('../models/User');

class ActivityService {
  
  // Helper function to get client IP
  static getClientIP(req) {
    return req.headers['x-forwarded-for'] || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           req.ip ||
           'unknown';
  }

  // Main function to log any activity
  static async logActivity(userId, action, details = {}, req = null) {
    try {
      console.log(`🔄 Attempting to log activity: ${action} for user: ${userId}`);

      if (!userId) {
        console.error('❌ Cannot log activity: userId is required');
        return false;
      }

      // Validate action - use the same enum as the Activity model
      const validActions = [
        'login', 'logout', 'create_meal_plan', 'update_meal_plan', 'delete_meal_plan',
        'create_meal', 'update_meal', 'delete_meal', 'update_profile',
        'made_admin', 'made_sub_admin', 'made_user', 'disabled_account', 'enabled_account',
        'deleted_feedback', 'submitted_feedback', 'updated_feedback_status',
        'password_reset', 'email_verification', 'signup'
      ];
      if (!validActions.includes(action)) {
        console.error(`❌ Invalid action: ${action}. Valid actions:`, validActions);
        return false;
      }

      // Get user information
      let userInfo = {};
      try {
        const user = await User.findById(userId).select('username email firstName lastName');
        if (user) {
          userInfo = {
            username: user.username,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName
          };
        }
      } catch (userError) {
        console.error('⚠️ Could not fetch user info:', userError);
      }

      // Prepare activity data
      const activityData = {
        user: userId,
        action: action,
        details: {
          ...details,
          userInfo,
          timestamp: new Date(),
          userAgent: req ? req.headers['user-agent'] : 'unknown'
        },
        ipAddress: req ? this.getClientIP(req) : 'unknown'
      };

      // Create and save activity
      const activity = new Activity(activityData);
      await activity.save();
      
      console.log(`✅ Activity logged successfully: ${action} for user: ${userId}`);
      return true;
      
    } catch (error) {
      console.error(`❌ Error logging activity ${action}:`, error);
      return false;
    }
  }

  // Specific activity logging methods
  static async logLogin(userId, email, req) {
    return this.logActivity(userId, 'login', {
      email: email,
      loginTime: new Date(),
      action: 'User logged in'
    }, req);
  }

  static async logLogout(userId, req) {
    return this.logActivity(userId, 'logout', {
      logoutTime: new Date(),
      action: 'User logged out'
    }, req);
  }

  static async logProfileUpdate(userId, updatedFields, req) {
    return this.logActivity(userId, 'update_profile', {
      updatedFields: updatedFields,
      action: 'User updated profile',
      fieldsChanged: Object.keys(updatedFields)
    }, req);
  }

  static async logMealPlanCreate(userId, mealPlanData, req) {
    return this.logActivity(userId, 'create_meal_plan', {
      date: mealPlanData.date,
      mealType: mealPlanData.mealType,
      mealName: mealPlanData.mealName || 'Unknown meal',
      action: 'User created meal plan'
    }, req);
  }

  static async logMealPlanUpdate(userId, mealPlanData, req) {
    return this.logActivity(userId, 'update_meal_plan', {
      date: mealPlanData.date,
      mealType: mealPlanData.mealType,
      mealName: mealPlanData.mealName || 'Unknown meal',
      action: 'User updated meal plan'
    }, req);
  }

  static async logMealPlanDelete(userId, date, req) {
    return this.logActivity(userId, 'delete_meal_plan', {
      date: date,
      action: 'User deleted meal plan'
    }, req);
  }

  static async logMealCreate(userId, mealData, req) {
    return this.logActivity(userId, 'create_meal', {
      mealName: mealData.name,
      category: mealData.category,
      calories: mealData.calories,
      action: 'User created meal'
    }, req);
  }

  // Get activities with pagination and filtering
  static async getActivities(filters = {}, page = 1, limit = 50) {
    try {
      console.log('🔍 Fetching activities with filters:', filters);
      
      // Build MongoDB aggregation pipeline
      const pipeline = [
        // Lookup user information
        {
          $lookup: {
            from: 'users',
            localField: 'user',
            foreignField: '_id',
            as: 'userInfo'
          }
        },
        {
          $unwind: {
            path: '$userInfo',
            preserveNullAndEmptyArrays: true
          }
        }
      ];

      // Add filters
      const matchStage = {};
      
      if (filters.action && filters.action !== 'all') {
        matchStage.action = filters.action;
      }
      
      if (filters.user) {
        matchStage.$or = [
          { 'userInfo.username': { $regex: filters.user, $options: 'i' } },
          { 'userInfo.email': { $regex: filters.user, $options: 'i' } }
        ];
      }
      
      if (filters.dateFrom || filters.dateTo) {
        matchStage.createdAt = {};
        if (filters.dateFrom) {
          matchStage.createdAt.$gte = new Date(filters.dateFrom);
        }
        if (filters.dateTo) {
          matchStage.createdAt.$lte = new Date(filters.dateTo + 'T23:59:59.999Z');
        }
      }

      if (Object.keys(matchStage).length > 0) {
        pipeline.push({ $match: matchStage });
      }

      // Add sorting
      pipeline.push({ $sort: { createdAt: -1 } });

      // Get total count
      const countPipeline = [...pipeline, { $count: 'total' }];
      const countResult = await Activity.aggregate(countPipeline);
      const total = countResult.length > 0 ? countResult[0].total : 0;

      // Add pagination
      const skip = (page - 1) * limit;
      pipeline.push({ $skip: skip }, { $limit: limit });

      // Execute query
      const activities = await Activity.aggregate(pipeline);

      console.log(`✅ Found ${activities.length} activities (${total} total)`);

      return {
        activities: activities.map(activity => ({
          _id: activity._id,
          action: activity.action,
          details: activity.details,
          ipAddress: activity.ipAddress,
          createdAt: activity.createdAt,
          username: activity.userInfo?.username || 'Unknown',
          email: activity.userInfo?.email || 'Unknown',
          description: this.getActionDescription(activity.action, activity.details)
        })),
        totalActivities: total,
        currentPage: page,
        totalPages: Math.ceil(total / limit)
      };
      
    } catch (error) {
      console.error('❌ Error fetching activities:', error);
      throw error;
    }
  }

  // Get human-readable description for actions
  static getActionDescription(action, details) {
    switch (action) {
      case 'login':
        return `User logged in from ${details.userAgent || 'unknown device'}`;
      case 'logout':
        return 'User logged out';
      case 'signup':
        return 'User created a new account';
      case 'update_profile':
        return `User updated profile (${details.fieldsChanged?.join(', ') || 'unknown fields'})`;
      case 'create_meal_plan':
        return `User created meal plan for ${details.date} (${details.mealType})`;
      case 'update_meal_plan':
        return `User updated meal plan for ${details.date} (${details.mealType})`;
      case 'delete_meal_plan':
        return `User deleted meal plan for ${details.date}`;
      case 'create_meal':
        return `User created meal: ${details.mealName}`;
      case 'update_meal':
        return `User updated meal: ${details.mealName}`;
      case 'delete_meal':
        return `User deleted meal: ${details.mealName}`;
      case 'made_admin':
        return `Admin promoted ${details.targetUser || 'user'} to Admin`;
      case 'made_sub_admin':
        return `Admin promoted ${details.targetUser || 'user'} to Sub-Admin`;
      case 'made_user':
        return `Admin changed ${details.targetUser || 'user'} role to User`;
      case 'disabled_account':
        return `Admin disabled account for ${details.targetUser || 'user'}`;
      case 'enabled_account':
        return `Admin enabled account for ${details.targetUser || 'user'}`;
      case 'deleted_feedback':
        return `Admin deleted feedback: ${details.feedbackSubject || 'Unknown feedback'}`;
      case 'submitted_feedback':
        return `User submitted feedback: ${details.feedbackSubject || 'Unknown feedback'}`;
      case 'updated_feedback_status':
        return `Admin updated feedback status to ${details.newStatus || 'unknown'}`;
      case 'password_reset':
        return 'User reset password';
      case 'email_verification':
        return 'User verified email address';
      default:
        return `User performed action: ${action}`;
    }
  }

  // Test function to create sample activities
  static async createTestActivities(userId, req) {
    const testActivities = [
      { action: 'login', details: { email: '<EMAIL>' } },
      { action: 'update_profile', details: { updatedFields: { firstName: 'Test' } } },
      { action: 'create_meal_plan', details: { date: '2024-01-15', mealType: 'breakfast' } }
    ];

    for (const testActivity of testActivities) {
      await this.logActivity(userId, testActivity.action, testActivity.details, req);
    }
    
    return testActivities.length;
  }
}

module.exports = ActivityService;
