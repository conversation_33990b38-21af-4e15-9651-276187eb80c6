import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useAuth } from '../../context/AuthContext';
import { aiAPI, mealPlansAPI, mealsAPI } from '../../services/api';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const AIChatScreen = ({ navigation }) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [selectedHealthCondition, setSelectedHealthCondition] = useState(null);
  const [showGoalSelection, setShowGoalSelection] = useState(true);
  const [showChatInput, setShowChatInput] = useState(false);
  const [goals, setGoals] = useState([]);
  const [healthConditions, setHealthConditions] = useState([]);
  const [generatedMealPlan, setGeneratedMealPlan] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [editingMealType, setEditingMealType] = useState(null);
  const [showMealTypeSelection, setShowMealTypeSelection] = useState(false);
  const [isEditingMode, setIsEditingMode] = useState(false);
  const [showServingSizeInput, setShowServingSizeInput] = useState(false);
  const [mealServingSizes, setMealServingSizes] = useState({});
  const [generatedWeeklyPlan, setGeneratedWeeklyPlan] = useState(null);
  const [showWeeklyPlanActions, setShowWeeklyPlanActions] = useState(false);
  const [showCalorieGoalInput, setShowCalorieGoalInput] = useState(false);
  const [calorieGoalInput, setCalorieGoalInput] = useState('');
  const [dailyBudget, setDailyBudget] = useState(1000); // Daily budget for AI meal plan generation

  const scrollViewRef = useRef();
  const { user } = useAuth();

  const defaultGoals = [
    { id: 'lose_weight', name: 'Lose Weight', description: 'Get meal suggestions to help with weight loss' },
    { id: 'build_muscle', name: 'Build Muscle', description: 'Get high-protein meals to support muscle building' },
    { id: 'manage_health', name: 'Manage a Health Condition', description: 'Get meals tailored to specific health conditions' },
    { id: 'eat_sustainably', name: 'Eat Sustainably', description: 'Get environmentally conscious meal suggestions' },
    { id: 'set_calorie_goal', name: 'Set Calorie Goal', description: 'Set your daily calorie target and get meal suggestions based on your goal' },
    { id: 'generate_meal_plan', name: 'Generate a meal plan', description: 'Create a personalized daily meal plan based on your dietary preferences' },
    { id: 'generate_weekly_plan', name: 'Generate a 7-day meal plan', description: 'Create a complete weekly meal plan for your family with variety and balance' },
    { id: 'other', name: 'Other', description: 'Chat directly with AI for custom dietary advice' }
  ];

  const defaultHealthConditions = [
    { id: 'type2_diabetes', name: 'Type 2 Diabetes', description: 'Low-sugar, low-carb meal recommendations' },
    { id: 'celiac_disease', name: 'Celiac Disease', description: 'Gluten-free meal recommendations' },
    { id: 'hypertension', name: 'Hypertension', description: 'Low-sodium meal recommendations' },
    { id: 'heart_disease', name: 'Heart Disease', description: 'Heart-healthy, low-cholesterol meals' },
    { id: 'lactose_intolerance', name: 'Lactose Intolerance', description: 'Dairy-free meal recommendations' }
  ];

  useEffect(() => {
    loadGoalsAndConditions();
    addWelcomeMessage();
    loadBudgetPreferences();
  }, []);

  const loadBudgetPreferences = async () => {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const savedBudget = await AsyncStorage.getItem('mealPlanBudget');
      if (savedBudget) {
        const budget = parseInt(savedBudget);
        if (budget >= 200 && budget <= 15000) {
          setDailyBudget(budget);
        }
      }
    } catch (error) {
      console.error('Error loading budget preferences:', error);
      setDailyBudget(1000); // Default fallback
    }
  };

  const loadGoalsAndConditions = async () => {
    try {
      console.log('🎯 Loading goals from API...');
      const response = await aiAPI.getGoals();
      console.log('🎯 API Response:', response);

      if (response.data.success) {
        console.log('🎯 API goals loaded:', response.data.goals);
        setGoals(response.data.goals || defaultGoals);
        setHealthConditions(response.data.healthConditions || defaultHealthConditions);
      } else {
        console.log('🎯 API response not successful, using default goals');
        setGoals(defaultGoals);
        setHealthConditions(defaultHealthConditions);
      }
    } catch (error) {
      console.error('🎯 Error loading goals, using default goals:', error);
      setGoals(defaultGoals);
      setHealthConditions(defaultHealthConditions);
    }

    console.log('🎯 Final goals state will be set to:', goals.length > 0 ? goals : defaultGoals);
  };

  const addWelcomeMessage = () => {
    const userName = user?.firstName || 'User';
    const welcomeMessage = {
      id: Date.now(),
      text: `Hello ${userName}! I'm your AI meal planning assistant. To get started, please select one of your health goals below, and I'll provide personalized dietary recommendations for you and your family.`,
      isUser: false,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  };

  const selectGoal = async (goal) => {
    setSelectedGoal(goal);
    setShowGoalSelection(false);

    const goalMessage = {
      id: Date.now(),
      text: goal.id === 'other' ? 'I want to chat about something else' :
            goal.id === 'generate_meal_plan' ? 'I want to generate a meal plan' :
            goal.id === 'set_calorie_goal' ? 'I want to set my calorie goal' :
            `I want to ${goal.name.toLowerCase()}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, goalMessage]);

    if (goal.id === 'other') {
      // For "Other", enable free chat mode permanently
      const aiMessage = {
        id: Date.now() + 1,
        text: "I'm here to help with any dietary questions or meal planning needs you have! Please tell me what you'd like to know about nutrition, meals, or healthy eating.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
      setShowChatInput(true);
    } else if (goal.id === 'manage_health') {
      // Show health condition selection
      const responseMessage = {
        id: Date.now() + 1,
        text: "Great! Please select the specific health condition you'd like to manage:",
        isUser: false,
        timestamp: new Date(),
        showHealthConditions: true,
      };

      setMessages(prev => [...prev, responseMessage]);
    } else if (goal.id === 'set_calorie_goal') {
      // Show calorie goal input
      setShowCalorieGoalInput(true);
    } else if (goal.id === 'generate_meal_plan') {
      // Generate AI meal plan
      await generateAIMealPlan();
    } else if (goal.id === 'generate_weekly_plan') {
      // Generate 7-day weekly meal plan
      await generateWeeklyMealPlan();
    } else {
      // Get AI suggestions for other goals
      await getGoalSuggestions(goal);
    }
  };

  const selectHealthCondition = async (condition) => {
    setSelectedHealthCondition(condition);

    const conditionMessage = {
      id: Date.now(),
      text: `I want to manage ${condition.name}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, conditionMessage]);

    // Get AI suggestions for the selected health condition
    await getGoalSuggestions(selectedGoal, condition);
  };

  const getGoalSuggestions = async (goal, healthCondition = null) => {
    try {
      setLoading(true);

      const response = await aiAPI.getGoalSuggestions({
        goal: goal.name,
        healthCondition: healthCondition?.name
      });

      if (response.data.success && response.data.suggestions) {
        const suggestions = response.data.suggestions;
        let responseText = suggestions.explanation;

        if (suggestions.recommendedRestrictions.length > 0) {
          responseText += `\n\n🥗 Recommended Dietary Restrictions:\n${suggestions.recommendedRestrictions.map(r => `• ${r}`).join('\n')}`;
        }

        if (suggestions.recommendedAllergies.length > 0) {
          responseText += `\n\n⚠️ Consider avoiding:\n${suggestions.recommendedAllergies.map(a => `• ${a}`).join('\n')}`;
        }

        if (suggestions.additionalTips.length > 0) {
          responseText += `\n\n💡 Additional Tips:\n${suggestions.additionalTips.map(tip => `• ${tip}`).join('\n')}`;
        }

        responseText += `\n\nWould you like me to recommend specific meals from our database that match these preferences?`;

        const aiMessage = {
          id: Date.now(),
          text: responseText,
          isUser: false,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, aiMessage]);

        // Enable chat input after AI responds so user can continue conversation
        setShowChatInput(true);
      }
    } catch (error) {
      console.error('Error getting goal suggestions:', error);
      const errorMessage = {
        id: Date.now(),
        text: "I'm sorry, I'm having trouble providing suggestions right now. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);

      // Enable chat input even on error so user can try again
      setShowChatInput(true);
    } finally {
      setLoading(false);
    }
  };

  const submitCalorieGoal = async () => {
    const calorieGoal = parseInt(calorieGoalInput);

    if (!calorieGoal || calorieGoal < 1000 || calorieGoal > 5000) {
      Alert.alert('Invalid Input', 'Please enter a valid calorie goal between 1000 and 5000 calories.');
      return;
    }

    setShowCalorieGoalInput(false);
    setCalorieGoalInput('');

    const calorieMessage = {
      id: Date.now(),
      text: `My daily calorie goal is: ${calorieGoal} calories`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, calorieMessage]);

    try {
      setLoading(true);

      const loadingMessage = {
        id: Date.now() + 1,
        text: `Perfect! I'm setting your calorie goal to ${calorieGoal} calories and finding meal recommendations that fit your target. This may take a moment...`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, loadingMessage]);

      const response = await aiAPI.setCalorieGoal(calorieGoal);

      if (response.data.success) {
        let responseText = response.data.personalizedMessage;

        if (response.data.recommendations && response.data.recommendations.length > 0) {
          responseText += '\n\n🍽️ Here are some meal recommendations that fit your calorie goal:\n\n';
          response.data.recommendations.forEach((rec, index) => {
            responseText += `${index + 1}. **${rec.mealName}** (${rec.calories} calories)\n`;
            responseText += `   📍 ${rec.mealType} | ${rec.reason}\n`;
            if (rec.portionGuidance) {
              responseText += `   💡 ${rec.portionGuidance}\n`;
            }
            responseText += '\n';
          });
        }

        if (response.data.nutritionalTips && response.data.nutritionalTips.length > 0) {
          responseText += '\n💡 **Nutritional Tips:**\n';
          response.data.nutritionalTips.forEach(tip => {
            responseText += `• ${tip}\n`;
          });
        }

        const aiMessage = {
          id: Date.now() + 2,
          text: responseText,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);

        // Enable chat input for follow-up questions
        setShowChatInput(true);
      }
    } catch (error) {
      console.error('Error setting calorie goal:', error);
      const errorMessage = {
        id: Date.now() + 2,
        text: "I'm sorry, I had trouble setting your calorie goal. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const generateAIMealPlan = async () => {
    try {
      setLoading(true);

      const loadingMessage = {
        id: Date.now(),
        text: "Perfect! I'm generating a personalized meal plan based on your dietary preferences. This may take a moment...",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, loadingMessage]);

      const response = await aiAPI.generateMealPlan({ budgetPerDay: dailyBudget });
      console.log('🔍 AI Meal Plan Response:', JSON.stringify(response, null, 2));

      if (response.data && response.data.success) {
        setGeneratedMealPlan(response.data);

        // Format the meal plan for display
        const mealPlanText = formatMealPlanForDisplay(response.data);
        console.log('🍽️ Formatted Meal Plan Text:', mealPlanText);

        const aiMessage = {
          id: Date.now() + 1,
          text: `${response.data.personalizedMessage}\n\n${mealPlanText}\n\n${response.data.nutritionalSummary}\n\nBefore we save this meal plan, let me ask: How many servings would you like for each dish? This will help me calculate the right portions for your family.`,
          isUser: false,
          timestamp: new Date(),
          showServingSizeInput: true,
        };
        setMessages(prev => [...prev, aiMessage]);
        setShowServingSizeInput(true);

        // Initialize serving sizes with default value of 1 for each meal
        const initialServingSizes = {};
        ['breakfast', 'lunch', 'dinner', 'snacks'].forEach(mealType => {
          if (response.data.mealPlan[mealType]) {
            response.data.mealPlan[mealType].forEach((meal, index) => {
              const key = `${mealType}-${index}`;
              initialServingSizes[key] = 1;
            });
          }
        });
        setMealServingSizes(initialServingSizes);
      }
    } catch (error) {
      console.error('Error generating meal plan:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I had trouble generating your meal plan. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const generateWeeklyMealPlan = async () => {
    try {
      setLoading(true);

      const loadingMessage = {
        id: Date.now(),
        text: "Excellent! I'm creating a comprehensive 7-day meal plan for your family. This will include breakfast, lunch, and dinner for each day with MAXIMUM VARIETY and no repeated meals. Please give me a moment...",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, loadingMessage]);

      // Generate weekly meal plan with diversity
      const response = await aiAPI.generateWeeklyMealPlan({ budgetPerDay: dailyBudget });

      if (response.data && response.data.success && response.data.weeklyMealPlan) {
        // Convert the weekly meal plan to the expected format
        const weeklyPlans = [];
        const dayNames = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'];
        const dayKeys = ['day1', 'day2', 'day3', 'day4', 'day5', 'day6', 'day7'];

        dayKeys.forEach((dayKey, index) => {
          // Always add the day, even if it has empty meals
          weeklyPlans.push({
            day: index,
            dayName: dayNames[index],
            mealPlan: response.data.weeklyMealPlan[dayKey] || {
              breakfast: [],
              lunch: [],
              dinner: [],
              snacks: []
            },
            nutritionalSummary: response.data.nutritionalSummary
          });
        });

        // Check if we have the expected 21 meals (3 per day × 7 days)
        const totalMeals = weeklyPlans.reduce((total, dayPlan) => {
          return total +
            (dayPlan.mealPlan.breakfast?.length || 0) +
            (dayPlan.mealPlan.lunch?.length || 0) +
            (dayPlan.mealPlan.dinner?.length || 0);
        }, 0);

        const expectedMeals = 21; // 7 days × 3 meals per day
        const minimumAcceptableMeals = 15; // Allow some flexibility

        if (weeklyPlans.length === 7 && totalMeals >= minimumAcceptableMeals) {
          setGeneratedWeeklyPlan(weeklyPlans);

          // Format the weekly meal plan for display
          const weeklyPlanText = formatWeeklyMealPlanForDisplay(weeklyPlans);

          const aiMessage = {
            id: Date.now() + 1,
            text: `🗓️ **Your 7-Day Family Meal Plan is Ready!**\n\n${response.data.personalizedMessage}\n\n${weeklyPlanText}\n\n**Diversity Analysis:** ${response.data.diversityAnalysis}\n\n**Total Meals:** ${totalMeals} meals planned across 7 days\n\nWould you like to save this weekly meal plan to your calendar?`,
            isUser: false,
            timestamp: new Date(),
            showWeeklyPlanActions: true,
          };
          setMessages(prev => [...prev, aiMessage]);
          setShowWeeklyPlanActions(true);
        } else {
          console.warn('Weekly meal plan validation failed:', {
            weeklyPlansLength: weeklyPlans.length,
            totalMeals,
            expectedMeals,
            minimumAcceptableMeals,
            responseData: response.data
          });
          throw new Error(`Insufficient meals generated: ${totalMeals}/${expectedMeals} meals (minimum ${minimumAcceptableMeals} required)`);
        }
      } else {
        console.warn('Weekly meal plan response validation failed:', response.data);
        throw new Error('Failed to generate weekly meal plan - invalid response format');
      }
    } catch (error) {
      console.error('Error generating weekly meal plan:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I had trouble generating your 7-day meal plan. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const formatWeeklyMealPlanForDisplay = (weeklyPlans) => {
    let text = '';

    weeklyPlans.forEach((dayPlan, index) => {
      text += `**${dayPlan.dayName}:**\n`;

      // Breakfast
      if (dayPlan.mealPlan.breakfast && dayPlan.mealPlan.breakfast.length > 0) {
        text += `🌅 Breakfast: ${dayPlan.mealPlan.breakfast[0].mealName}\n`;
      }

      // Lunch
      if (dayPlan.mealPlan.lunch && dayPlan.mealPlan.lunch.length > 0) {
        text += `☀️ Lunch: ${dayPlan.mealPlan.lunch[0].mealName}\n`;
      }

      // Dinner
      if (dayPlan.mealPlan.dinner && dayPlan.mealPlan.dinner.length > 0) {
        text += `🌙 Dinner: ${dayPlan.mealPlan.dinner[0].mealName}\n`;
      }

      text += '\n';
    });

    return text;
  };



  // Helper function to find meal data from database
  const findMealData = async (mealName) => {
    try {
      const response = await mealsAPI.getAllMeals();
      const allMeals = response.data;
      const foundMeal = allMeals.find(meal =>
        meal.name.toLowerCase() === mealName.toLowerCase()
      );
      return foundMeal || null;
    } catch (error) {
      console.error('Error fetching meal data:', error);
      return null;
    }
  };

  const formatMealPlanForDisplay = (mealPlanData, servingSizes = null) => {
    console.log('🔍 formatMealPlanForDisplay received:', JSON.stringify(mealPlanData, null, 2));
    const { mealPlan } = mealPlanData;
    let text = "🍽️ **Your Personalized Meal Plan:**\n\n";

    if (mealPlan && mealPlan.breakfast && mealPlan.breakfast.length > 0) {
      text += "🌅 **Breakfast:**\n";
      mealPlan.breakfast.forEach((meal, index) => {
        console.log(`🍳 Breakfast meal ${index + 1}:`, meal);
        const servingKey = `breakfast-${index}`;
        const servings = servingSizes && servingSizes[servingKey] ? servingSizes[servingKey] : 1;
        const servingText = servings > 1 ? ` (${servings} servings)` : '';
        text += `• ${meal.mealName || 'Unknown meal'}${servingText}\n  ${meal.reason || 'No reason provided'}\n\n`;
      });
    }

    if (mealPlan && mealPlan.lunch && mealPlan.lunch.length > 0) {
      text += "☀️ **Lunch:**\n";
      mealPlan.lunch.forEach((meal, index) => {
        console.log(`🍽️ Lunch meal ${index + 1}:`, meal);
        const servingKey = `lunch-${index}`;
        const servings = servingSizes && servingSizes[servingKey] ? servingSizes[servingKey] : 1;
        const servingText = servings > 1 ? ` (${servings} servings)` : '';
        text += `• ${meal.mealName || 'Unknown meal'}${servingText}\n  ${meal.reason || 'No reason provided'}\n\n`;
      });
    }

    if (mealPlan && mealPlan.dinner && mealPlan.dinner.length > 0) {
      text += "🌙 **Dinner:**\n";
      mealPlan.dinner.forEach((meal, index) => {
        console.log(`🌙 Dinner meal ${index + 1}:`, meal);
        const servingKey = `dinner-${index}`;
        const servings = servingSizes && servingSizes[servingKey] ? servingSizes[servingKey] : 1;
        const servingText = servings > 1 ? ` (${servings} servings)` : '';
        text += `• ${meal.mealName || 'Unknown meal'}${servingText}\n  ${meal.reason || 'No reason provided'}\n\n`;
      });
    }

    if (mealPlan && mealPlan.snacks && mealPlan.snacks.length > 0) {
      text += "🍿 **Snacks:**\n";
      mealPlan.snacks.forEach((meal, index) => {
        console.log(`🍿 Snack ${index + 1}:`, meal);
        const servingKey = `snacks-${index}`;
        const servings = servingSizes && servingSizes[servingKey] ? servingSizes[servingKey] : 1;
        const servingText = servings > 1 ? ` (${servings} servings)` : '';
        text += `• ${meal.mealName || 'Unknown meal'}${servingText}\n  ${meal.reason || 'No reason provided'}\n\n`;
      });
    }

    return text;
  };

  const handleServingSizeConfirmation = () => {
    setShowServingSizeInput(false);

    // Show the meal plan with serving sizes and actions
    const mealPlanText = formatMealPlanForDisplay(generatedMealPlan, mealServingSizes);

    const aiMessage = {
      id: Date.now(),
      text: `Perfect! Here's your meal plan with the serving sizes you selected:\n\n${mealPlanText}\n\nWhat would you like to do with this meal plan?`,
      isUser: false,
      timestamp: new Date(),
      showMealPlanActions: true,
    };
    setMessages(prev => [...prev, aiMessage]);
    setShowChatInput(true);
  };

  const handleSaveWeeklyMealPlan = async (startDate) => {
    if (!generatedWeeklyPlan) return;

    try {
      setLoading(true);

      const loadingMessage = {
        id: Date.now(),
        text: "Saving your 7-day meal plan to the calendar...",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, loadingMessage]);

      // Save each day's meal plan using the same format as regular meal plans
      for (let dayIndex = 0; dayIndex < generatedWeeklyPlan.length; dayIndex++) {
        const dayPlan = generatedWeeklyPlan[dayIndex];

        // Calculate the date for this day
        const currentDate = new Date(startDate);
        currentDate.setDate(currentDate.getDate() + dayIndex);

        // Convert the day's meal plan to the same format as regular meal plans
        const mealPlanData = await convertWeeklyDayToSaveFormat(dayPlan, currentDate);

        // Save using the same API as regular meal plans
        await mealPlansAPI.saveMealPlan(mealPlanData);
      }

      const successMessage = {
        id: Date.now() + 1,
        text: `🎉 Success! Your 7-day meal plan has been saved to your calendar starting from ${startDate.toLocaleDateString()}. You can view and manage it in the Calendar tab.`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, successMessage]);

      // Reset states
      setGeneratedWeeklyPlan(null);
      setShowWeeklyPlanActions(false);
      setShowDatePicker(false);
      setShowChatInput(true);

    } catch (error) {
      console.error('Error saving weekly meal plan:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "Sorry, there was an error saving your weekly meal plan. Please try again.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const convertWeeklyDayToSaveFormat = async (dayPlan, date) => {
    const meals = [];
    const selectedDate = new Date(date);
    const year = selectedDate.getFullYear();
    const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
    const day = String(selectedDate.getDate()).padStart(2, '0');
    const dateStr = `${year}-${month}-${day}`;

    // Convert breakfast meals
    if (dayPlan.mealPlan.breakfast) {
      for (const meal of dayPlan.mealPlan.breakfast) {
        const mealData = await findMealData(meal.mealName);
        meals.push({
          date: dateStr,
          mealType: 'breakfast',
          servings: 1, // Default serving size for weekly plans
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-breakfast-${dateStr}`,
            calories: mealData?.calories || 0,
            protein: mealData?.protein || 0,
            carbs: mealData?.carbs || 0,
            fat: mealData?.fat || 0,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason
          }
        });
      }
    }

    // Convert lunch meals
    if (dayPlan.mealPlan.lunch) {
      for (const meal of dayPlan.mealPlan.lunch) {
        const mealData = await findMealData(meal.mealName);
        meals.push({
          date: dateStr,
          mealType: 'lunch',
          servings: 1,
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-lunch-${dateStr}`,
            calories: mealData?.calories || 0,
            protein: mealData?.protein || 0,
            carbs: mealData?.carbs || 0,
            fat: mealData?.fat || 0,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason
          }
        });
      }
    }

    // Convert dinner meals
    if (dayPlan.mealPlan.dinner) {
      for (const meal of dayPlan.mealPlan.dinner) {
        const mealData = await findMealData(meal.mealName);
        meals.push({
          date: dateStr,
          mealType: 'dinner',
          servings: 1,
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-dinner-${dateStr}`,
            calories: mealData?.calories || 0,
            protein: mealData?.protein || 0,
            carbs: mealData?.carbs || 0,
            fat: mealData?.fat || 0,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason
          }
        });
      }
    }

    // Return the same format as convertAIMealPlanToSaveFormat
    return {
      name: `AI Generated 7-Day Plan - ${dayPlan.dayName} (${new Date(date).toLocaleDateString()})`,
      startDate: dateStr,
      endDate: dateStr,
      dietaryPreference: 'all',
      meals: meals,
      mealTimes: {
        breakfast: '08:00',
        lunch: '12:00',
        dinner: '18:00',
        snacks: '15:00'
      }
    };
  };

  const handleSaveMealPlan = async (date) => {
    try {
      setLoading(true);

      if (!generatedMealPlan) {
        throw new Error('No meal plan to save');
      }

      // Check if we're in editing mode
      if (editingMealType) {
        // Show meal options for editing
        const aiMessage = {
          id: Date.now(),
          text: `Perfect! Now I'll show you alternative ${editingMealType} options that match your family's dietary preferences. Please tell me which specific ${editingMealType} you'd like to replace, and I'll suggest alternatives.`,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
        setShowDatePicker(false);
        setShowChatInput(true);
        return;
      }

      // Convert AI meal plan to the format expected by saveMealPlan API
      const mealPlanData = await convertAIMealPlanToSaveFormat(generatedMealPlan, date);

      console.log('🍽️ AI Chatbot saving meal plan data:', JSON.stringify(mealPlanData, null, 2));

      const response = await mealPlansAPI.saveMealPlan(mealPlanData);

      if (response.data && response.data.success) {
        const successMessage = {
          id: Date.now(),
          text: `Meal plan saved successfully for ${new Date(date).toLocaleDateString()}`,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, successMessage]);

        // Reset meal plan state
        setGeneratedMealPlan(null);
        setShowDatePicker(false);
        setEditingMealType(null);
      }
    } catch (error) {
      console.error('Error saving meal plan:', error);
      const errorMessage = {
        id: Date.now(),
        text: "I'm sorry, I had trouble saving your meal plan. Please try again.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const convertAIMealPlanToSaveFormat = async (aiMealPlan, date) => {
    const meals = [];
    // Fix timezone issue by using local date formatting instead of UTC
    const selectedDate = new Date(date);
    const year = selectedDate.getFullYear();
    const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
    const day = String(selectedDate.getDate()).padStart(2, '0');
    const dateStr = `${year}-${month}-${day}`;

    // Convert breakfast meals
    if (aiMealPlan.mealPlan.breakfast) {
      for (const [index, meal] of aiMealPlan.mealPlan.breakfast.entries()) {
        const mealData = await findMealData(meal.mealName);
        const servingKey = `breakfast-${index}`;
        const servings = mealServingSizes[servingKey] || 1;

        meals.push({
          date: dateStr,
          mealType: 'breakfast',
          portions: servings, // Use portions field to match backend schema
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-breakfast`,
            calories: (mealData?.calories || 0) * servings,
            protein: (mealData?.protein || 0) * servings,
            carbs: (mealData?.carbs || 0) * servings,
            fat: (mealData?.fat || 0) * servings,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason,
            servingSize: servings // Store original serving size for reference
          }
        });
      }
    }

    // Convert lunch meals
    if (aiMealPlan.mealPlan.lunch) {
      for (const [index, meal] of aiMealPlan.mealPlan.lunch.entries()) {
        const mealData = await findMealData(meal.mealName);
        const servingKey = `lunch-${index}`;
        const servings = mealServingSizes[servingKey] || 1;

        meals.push({
          date: dateStr,
          mealType: 'lunch',
          portions: servings, // Use portions field to match backend schema
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-lunch`,
            calories: (mealData?.calories || 0) * servings,
            protein: (mealData?.protein || 0) * servings,
            carbs: (mealData?.carbs || 0) * servings,
            fat: (mealData?.fat || 0) * servings,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason,
            servingSize: servings // Store original serving size for reference
          }
        });
      }
    }

    // Convert dinner meals
    if (aiMealPlan.mealPlan.dinner) {
      for (const [index, meal] of aiMealPlan.mealPlan.dinner.entries()) {
        const mealData = await findMealData(meal.mealName);
        const servingKey = `dinner-${index}`;
        const servings = mealServingSizes[servingKey] || 1;

        meals.push({
          date: dateStr,
          mealType: 'dinner',
          portions: servings, // Use portions field to match backend schema
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-dinner`,
            calories: (mealData?.calories || 0) * servings,
            protein: (mealData?.protein || 0) * servings,
            carbs: (mealData?.carbs || 0) * servings,
            fat: (mealData?.fat || 0) * servings,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason,
            servingSize: servings // Store original serving size for reference
          }
        });
      }
    }

    // Convert snack meals
    if (aiMealPlan.mealPlan.snacks) {
      for (const [index, meal] of aiMealPlan.mealPlan.snacks.entries()) {
        const mealData = await findMealData(meal.mealName);
        const servingKey = `snacks-${index}`;
        const servings = mealServingSizes[servingKey] || 1;

        meals.push({
          date: dateStr,
          mealType: 'snack',
          portions: servings, // Use portions field to match backend schema
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-snack`,
            calories: (mealData?.calories || 0) * servings,
            protein: (mealData?.protein || 0) * servings,
            carbs: (mealData?.carbs || 0) * servings,
            fat: (mealData?.fat || 0) * servings,
            category: mealData?.category || 'AI Generated',
            dietaryTags: mealData?.dietaryTags || [],
            image: mealData?.image || '',
            ingredients: mealData?.ingredients || [],
            instructions: mealData?.instructions || [],
            description: meal.reason,
            servingSize: servings // Store original serving size for reference
          }
        });
      }
    }

    return {
      name: `AI Generated Meal Plan - ${new Date(date).toLocaleDateString()}`,
      startDate: dateStr,
      endDate: dateStr,
      dietaryPreference: 'all',
      meals: meals,
      mealTimes: {
        breakfast: '08:00',
        lunch: '12:00',
        dinner: '18:00',
        snacks: '15:00'
      }
    };
  };

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: inputText,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const messageText = inputText;
    setInputText('');
    setLoading(true);

    // Define direct edit patterns at the top level so they're accessible throughout
    const directEditPatterns = [
      /replace\s+(.+?)\s+with\s+(.+)/i,
      /change\s+(.+?)\s+to\s+(.+)/i,
      /swap\s+(.+?)\s+with\s+(.+)/i,
      /substitute\s+(.+?)\s+with\s+(.+)/i,
      /i want to replace\s+(.+?)\s+with\s+(.+)/i,
      /i want to change\s+(.+?)\s+to\s+(.+)/i,
      /i want to swap\s+(.+?)\s+with\s+(.+)/i
    ];

    const isDirectEditRequest = directEditPatterns.some(pattern => pattern.test(messageText.toLowerCase()));

    try {
      // Check if user is responding about meal plan actions
      if (generatedMealPlan) {
        // Check for Save option
        if (messageText.toLowerCase().includes('save') || messageText.toLowerCase().includes('calendar')) {
          const aiMessage = {
            id: Date.now() + 1,
            text: "Perfect! Please select the date you'd like to schedule this meal plan:",
            isUser: false,
            timestamp: new Date(),
            showDatePicker: true,
          };
          setMessages(prev => [...prev, aiMessage]);
          setShowDatePicker(true);
          return;
        }

        // If it's a direct edit request, process it immediately
        if (isDirectEditRequest && generatedMealPlan) {
          // This will be handled by the edit processing logic below
          // Don't return here, let it fall through to the edit processing
        } else {
          // Check for general edit keywords only if it's not a direct edit request
          const editKeywords = ['edit', 'update'];
          const hasGeneralEditKeyword = editKeywords.some(keyword => messageText.toLowerCase().includes(keyword));

          if (hasGeneralEditKeyword) {
            setIsEditingMode(true);
            const aiMessage = {
              id: Date.now() + 1,
              text: "Great! I can help you edit your meal plan. Please tell me which dishes you'd like to change. For example, you can say 'Replace Kare-Kare with Tapa in lunch' or 'Change breakfast to something vegetarian'.",
              isUser: false,
              timestamp: new Date(),
            };
            setMessages(prev => [...prev, aiMessage]);
            setShowChatInput(true);
            return;
          }
        }

        // Check for No thanks option (but not if it's a direct edit request)
        if (!isDirectEditRequest && (messageText.toLowerCase().includes('no') || messageText.toLowerCase().includes('not') || messageText.toLowerCase().includes('thanks'))) {
          const aiMessage = {
            id: Date.now() + 1,
            text: "No problem! Is there anything else I can help you with regarding your meal planning?",
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, aiMessage]);
          setGeneratedMealPlan(null);
          setIsEditingMode(false);
          return;
        }
      }

      // Check if user is in editing mode or making a direct edit request
      if ((isEditingMode || isDirectEditRequest) && generatedMealPlan) {
        try {
          // If this is a direct edit request, automatically enter edit mode
          if (isDirectEditRequest && !isEditingMode) {
            setIsEditingMode(true);
          }

          // Send the editing request to the dedicated edit endpoint
          // Check if this is a family plan by looking at the messages for isFamilyPlan flag
          const isFamilyPlan = messages.some(msg => msg.showMealPlanActions && msg.isFamilyPlan);

          const editResponse = await aiAPI.editMealPlan({
            currentMealPlan: generatedMealPlan.mealPlan,
            editRequest: messageText,
            isFamily: isFamilyPlan
          });
          console.log('🔍 Edit Meal Plan Response:', JSON.stringify(editResponse, null, 2));

          if (editResponse.data && editResponse.data.success) {
            // Update the generated meal plan with the edited version
            const updatedMealPlan = {
              mealPlan: editResponse.data.mealPlan,
              nutritionalSummary: editResponse.data.nutritionalSummary,
              personalizedMessage: editResponse.data.personalizedMessage,
              conflicts: editResponse.data.conflicts || [],
              timestamp: editResponse.data.timestamp
            };

            setGeneratedMealPlan(updatedMealPlan);

            // Format the meal plan for display
            const formattedMealPlan = formatMealPlanForDisplay(updatedMealPlan);

            const updatedMessage = {
              id: Date.now() + 1,
              text: `${updatedMealPlan.personalizedMessage}\n\n${formattedMealPlan}\n\nWhat would you like to do with this updated meal plan?`,
              isUser: false,
              timestamp: new Date(),
              showMealPlanActions: true,
            };
            setMessages(prev => [...prev, updatedMessage]);
            setIsEditingMode(false);
            return;
          } else {
            throw new Error(editResponse.data.message || 'Failed to edit meal plan');
          }
        } catch (error) {
          console.error('Error editing meal plan:', error);
          const errorMessage = {
            id: Date.now() + 1,
            text: "I'm sorry, I had trouble updating your meal plan. This could be because the meal you requested isn't available in our database. Please try with a different meal name or be more specific about your request. For example: 'Replace Pinakbet with Adobong Manok'.",
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, errorMessage]);
          return;
        }
      }

      const response = await aiAPI.chat({
        message: messageText,
        includeProfile: true,
        includeMeals: true
      });

      if (response.data.success) {
        const aiMessage = {
          id: Date.now() + 1,
          text: response.data.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I'm having trouble responding right now. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const resetChat = () => {
    setMessages([]);
    setSelectedGoal(null);
    setSelectedHealthCondition(null);
    setShowGoalSelection(true);
    setShowChatInput(false);
    addWelcomeMessage();
  };

  const renderMessage = (message) => (
    <View key={message.id} style={[
      styles.messageContainer,
      message.isUser ? styles.userMessage : styles.aiMessage
    ]}>
      <Text style={[
        styles.messageText,
        message.isUser ? styles.userMessageText : styles.aiMessageText
      ]}>
        {message.text}
      </Text>
      <Text style={styles.timestamp}>
        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </Text>

      {message.showHealthConditions && (
        <View style={styles.optionsContainer}>
          {healthConditions.map(condition => (
            <TouchableOpacity
              key={condition.id}
              style={styles.optionButton}
              onPress={() => selectHealthCondition(condition)}
            >
              <Text style={styles.optionButtonText}>{condition.name}</Text>
              <Text style={styles.optionDescription}>{condition.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {message.showServingSizeInput && generatedMealPlan && (
        <View style={styles.servingSizeContainer}>
          <Text style={styles.servingSizeTitle}>Set Serving Sizes for Each Dish:</Text>

          {/* Breakfast Servings */}
          {generatedMealPlan.mealPlan.breakfast && generatedMealPlan.mealPlan.breakfast.length > 0 && (
            <View style={styles.mealTypeSection}>
              <Text style={styles.mealTypeTitle}>🌅 Breakfast:</Text>
              {generatedMealPlan.mealPlan.breakfast.map((meal, index) => {
                const servingKey = `breakfast-${index}`;
                return (
                  <View key={servingKey} style={styles.servingInputRow}>
                    <Text style={styles.mealNameText}>{meal.mealName}</Text>
                    <View style={styles.servingControls}>
                      <TouchableOpacity
                        style={styles.servingButton}
                        onPress={() => {
                          const newServings = Math.max(1, (mealServingSizes[servingKey] || 1) - 1);
                          setMealServingSizes(prev => ({ ...prev, [servingKey]: newServings }));
                        }}
                      >
                        <Text style={styles.servingButtonText}>-</Text>
                      </TouchableOpacity>
                      <Text style={styles.servingCount}>{mealServingSizes[servingKey] || 1}</Text>
                      <TouchableOpacity
                        style={styles.servingButton}
                        onPress={() => {
                          const newServings = (mealServingSizes[servingKey] || 1) + 1;
                          setMealServingSizes(prev => ({ ...prev, [servingKey]: newServings }));
                        }}
                      >
                        <Text style={styles.servingButtonText}>+</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                );
              })}
            </View>
          )}

          {/* Lunch Servings */}
          {generatedMealPlan.mealPlan.lunch && generatedMealPlan.mealPlan.lunch.length > 0 && (
            <View style={styles.mealTypeSection}>
              <Text style={styles.mealTypeTitle}>☀️ Lunch:</Text>
              {generatedMealPlan.mealPlan.lunch.map((meal, index) => {
                const servingKey = `lunch-${index}`;
                return (
                  <View key={servingKey} style={styles.servingInputRow}>
                    <Text style={styles.mealNameText}>{meal.mealName}</Text>
                    <View style={styles.servingControls}>
                      <TouchableOpacity
                        style={styles.servingButton}
                        onPress={() => {
                          const newServings = Math.max(1, (mealServingSizes[servingKey] || 1) - 1);
                          setMealServingSizes(prev => ({ ...prev, [servingKey]: newServings }));
                        }}
                      >
                        <Text style={styles.servingButtonText}>-</Text>
                      </TouchableOpacity>
                      <Text style={styles.servingCount}>{mealServingSizes[servingKey] || 1}</Text>
                      <TouchableOpacity
                        style={styles.servingButton}
                        onPress={() => {
                          const newServings = (mealServingSizes[servingKey] || 1) + 1;
                          setMealServingSizes(prev => ({ ...prev, [servingKey]: newServings }));
                        }}
                      >
                        <Text style={styles.servingButtonText}>+</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                );
              })}
            </View>
          )}

          {/* Dinner Servings */}
          {generatedMealPlan.mealPlan.dinner && generatedMealPlan.mealPlan.dinner.length > 0 && (
            <View style={styles.mealTypeSection}>
              <Text style={styles.mealTypeTitle}>🌙 Dinner:</Text>
              {generatedMealPlan.mealPlan.dinner.map((meal, index) => {
                const servingKey = `dinner-${index}`;
                return (
                  <View key={servingKey} style={styles.servingInputRow}>
                    <Text style={styles.mealNameText}>{meal.mealName}</Text>
                    <View style={styles.servingControls}>
                      <TouchableOpacity
                        style={styles.servingButton}
                        onPress={() => {
                          const newServings = Math.max(1, (mealServingSizes[servingKey] || 1) - 1);
                          setMealServingSizes(prev => ({ ...prev, [servingKey]: newServings }));
                        }}
                      >
                        <Text style={styles.servingButtonText}>-</Text>
                      </TouchableOpacity>
                      <Text style={styles.servingCount}>{mealServingSizes[servingKey] || 1}</Text>
                      <TouchableOpacity
                        style={styles.servingButton}
                        onPress={() => {
                          const newServings = (mealServingSizes[servingKey] || 1) + 1;
                          setMealServingSizes(prev => ({ ...prev, [servingKey]: newServings }));
                        }}
                      >
                        <Text style={styles.servingButtonText}>+</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                );
              })}
            </View>
          )}

          {/* Snacks Servings */}
          {generatedMealPlan.mealPlan.snacks && generatedMealPlan.mealPlan.snacks.length > 0 && (
            <View style={styles.mealTypeSection}>
              <Text style={styles.mealTypeTitle}>🍎 Snacks:</Text>
              {generatedMealPlan.mealPlan.snacks.map((meal, index) => {
                const servingKey = `snacks-${index}`;
                return (
                  <View key={servingKey} style={styles.servingInputRow}>
                    <Text style={styles.mealNameText}>{meal.mealName}</Text>
                    <View style={styles.servingControls}>
                      <TouchableOpacity
                        style={styles.servingButton}
                        onPress={() => {
                          const newServings = Math.max(1, (mealServingSizes[servingKey] || 1) - 1);
                          setMealServingSizes(prev => ({ ...prev, [servingKey]: newServings }));
                        }}
                      >
                        <Text style={styles.servingButtonText}>-</Text>
                      </TouchableOpacity>
                      <Text style={styles.servingCount}>{mealServingSizes[servingKey] || 1}</Text>
                      <TouchableOpacity
                        style={styles.servingButton}
                        onPress={() => {
                          const newServings = (mealServingSizes[servingKey] || 1) + 1;
                          setMealServingSizes(prev => ({ ...prev, [servingKey]: newServings }));
                        }}
                      >
                        <Text style={styles.servingButtonText}>+</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                );
              })}
            </View>
          )}

          <TouchableOpacity
            style={styles.confirmServingsButton}
            onPress={handleServingSizeConfirmation}
          >
            <Text style={styles.confirmServingsButtonText}>Confirm Serving Sizes</Text>
          </TouchableOpacity>
        </View>
      )}

      {message.showMealPlanActions && generatedMealPlan && (
        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "Save to calendar",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              const aiMessage = {
                id: Date.now() + 1,
                text: "Perfect! Please select the date you'd like to schedule this meal plan:",
                isUser: false,
                timestamp: new Date(),
                showDatePicker: true,
              };
              setMessages(prev => [...prev, aiMessage]);
              setShowDatePicker(true);
            }}
          >
            <Text style={styles.actionButtonText}>Save to Calendar</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.editActionButton]}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "Edit this meal plan",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              setIsEditingMode(true);
              const aiMessage = {
                id: Date.now() + 1,
                text: "Great! I can help you edit your meal plan. Please tell me which dishes you'd like to change. For example, you can say 'Add Adobong kangkong as breakfast instead of Pinakbet' or 'Replace the lunch with Sinigang na baboy'.",
                isUser: false,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, aiMessage]);
              setShowChatInput(true);
            }}
          >
            <Text style={styles.editActionButtonText}>Edit/Update</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "No thanks",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              const aiMessage = {
                id: Date.now() + 1,
                text: "No problem! Is there anything else I can help you with regarding your meal planning?",
                isUser: false,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, aiMessage]);
              setGeneratedMealPlan(null);
              setIsEditingMode(false);
            }}
          >
            <Text style={styles.secondaryActionButtonText}>No Thanks</Text>
          </TouchableOpacity>
        </View>
      )}

      {message.showWeeklyPlanActions && generatedWeeklyPlan && (
        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "Save 7-day meal plan to calendar",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              const aiMessage = {
                id: Date.now() + 1,
                text: "Perfect! Please select the start date for your 7-day meal plan. The plan will be saved from your selected date through the next 6 days.",
                isUser: false,
                timestamp: new Date(),
                showDatePicker: true,
                isWeeklyPlan: true,
              };
              setMessages(prev => [...prev, aiMessage]);
              setShowDatePicker(true);
            }}
          >
            <Text style={styles.actionButtonText}>Save to Calendar</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "Generate a new 7-day meal plan",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              setGeneratedWeeklyPlan(null);
              setShowWeeklyPlanActions(false);
              generateWeeklyMealPlan();
            }}
          >
            <Text style={styles.secondaryActionButtonText}>Generate New Plan</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryActionButton]}
            onPress={() => {
              const userMessage = {
                id: Date.now(),
                text: "No thanks",
                isUser: true,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, userMessage]);

              const aiMessage = {
                id: Date.now() + 1,
                text: "No problem! Is there anything else I can help you with regarding your meal planning?",
                isUser: false,
                timestamp: new Date(),
              };
              setMessages(prev => [...prev, aiMessage]);
              setGeneratedWeeklyPlan(null);
              setShowWeeklyPlanActions(false);
            }}
          >
            <Text style={styles.secondaryActionButtonText}>No Thanks</Text>
          </TouchableOpacity>
        </View>
      )}

      {message.showDatePicker && showDatePicker && (
        <View style={styles.datePickerContainer}>
          <DateTimePicker
            value={selectedDate}
            mode="date"
            display="default"
            onChange={(event, date) => {
              if (date) {
                setSelectedDate(date);

                // Check if this is for a weekly plan or regular meal plan
                const lastMessage = messages[messages.length - 1];
                if (lastMessage && lastMessage.isWeeklyPlan) {
                  handleSaveWeeklyMealPlan(date);
                } else {
                  handleSaveMealPlan(date);
                }

                setShowDatePicker(false);
              }
            }}
            minimumDate={new Date()}
          />
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>AI Meal Assistant</Text>
        <TouchableOpacity onPress={resetChat}>
          <Ionicons name="refresh" size={24} color={colors.surface} />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
        >
          {messages.map(renderMessage)}
          
          {/* Goal Selection */}
          {showGoalSelection && messages.length > 0 && (
            <View style={styles.goalSelectionContainer}>
              <Text style={styles.goalSelectionTitle}>Select your goal:</Text>
              {goals.map(goal => (
                <TouchableOpacity
                  key={goal.id}
                  style={styles.goalButton}
                  onPress={() => selectGoal(goal)}
                >
                  <Text style={styles.goalButtonText}>{goal.name}</Text>
                  <Text style={styles.goalDescription}>{goal.description}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
          
          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingText}>AI is thinking...</Text>
            </View>
          )}
        </ScrollView>

        {/* Calorie Goal Input */}
        {showCalorieGoalInput && (
          <View style={styles.calorieGoalContainer}>
            <Text style={styles.calorieGoalTitle}>Set your daily calorie goal:</Text>
            <Text style={styles.calorieGoalSubtitle}>Enter your target daily calorie intake (1000-5000 calories)</Text>

            <View style={styles.calorieInputContainer}>
              <TextInput
                style={styles.calorieInput}
                value={calorieGoalInput}
                onChangeText={setCalorieGoalInput}
                placeholder="e.g., 2000"
                keyboardType="numeric"
                placeholderTextColor={colors.textSecondary}
              />
              <Text style={styles.calorieUnit}>calories</Text>
            </View>

            <View style={styles.calorieActions}>
              <TouchableOpacity
                style={[
                  styles.submitCalorieButton,
                  (!calorieGoalInput || parseInt(calorieGoalInput) < 1000 || parseInt(calorieGoalInput) > 8000) && styles.submitCalorieButtonDisabled
                ]}
                onPress={submitCalorieGoal}
                disabled={!calorieGoalInput || parseInt(calorieGoalInput) < 1000 || parseInt(calorieGoalInput) > 8000}
              >
                <Text style={styles.submitCalorieButtonText}>Set Goal & Get Recommendations</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.cancelCalorieButton}
                onPress={() => {
                  setShowCalorieGoalInput(false);
                  setCalorieGoalInput('');
                }}
              >
                <Text style={styles.cancelCalorieButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Input - Only show when chat input is enabled */}
        {showChatInput && (
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.textInput}
              placeholder="Ask me about meals, nutrition, or dietary advice..."
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
              placeholderTextColor={colors.textSecondary}
            />
            <TouchableOpacity
              style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
              onPress={sendMessage}
              disabled={!inputText.trim() || loading}
            >
              <Ionicons name="send" size={20} color={colors.surface} />
            </TouchableOpacity>
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  messagesContainer: {
    flex: 1,
    padding: spacing.md,
  },
  messageContainer: {
    marginBottom: spacing.md,
    maxWidth: '80%',
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: colors.primary,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
  },
  aiMessage: {
    alignSelf: 'flex-start',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  messageText: {
    fontSize: fonts.sizes.medium,
    lineHeight: 20,
  },
  userMessageText: {
    color: colors.surface,
  },
  aiMessageText: {
    color: colors.text,
  },
  timestamp: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    opacity: 0.7,
  },
  goalSelectionContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginTop: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  goalSelectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  goalButton: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  goalButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  goalDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  optionsContainer: {
    marginTop: spacing.md,
  },
  optionButton: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  optionButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  optionDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.md,
  },
  loadingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: spacing.md,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  textInput: {
    flex: 1,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    maxHeight: 100,
    marginRight: spacing.sm,
  },
  sendButton: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.textSecondary,
    opacity: 0.5,
  },
  actionButton: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.sm,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.surface,
  },
  secondaryActionButton: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  secondaryActionButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  editActionButton: {
    backgroundColor: colors.warning || '#FF9500',
    borderWidth: 1,
    borderColor: colors.warning || '#FF9500',
  },
  editActionButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.surface,
  },
  datePickerContainer: {
    marginTop: spacing.md,
    alignItems: 'center',
  },
  // Serving Size Styles
  servingSizeContainer: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginTop: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  servingSizeTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  mealTypeSection: {
    marginBottom: spacing.lg,
  },
  mealTypeTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  servingInputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.small,
    marginBottom: spacing.xs,
    borderWidth: 1,
    borderColor: colors.border,
  },
  mealNameText: {
    flex: 1,
    fontSize: fonts.sizes.small,
    color: colors.text,
    marginRight: spacing.sm,
  },
  servingControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  servingButton: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.small,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  servingButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.surface,
  },
  servingCount: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginHorizontal: spacing.md,
    minWidth: 30,
    textAlign: 'center',
  },
  confirmServingsButton: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  confirmServingsButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.surface,
  },
  // Calorie Goal Input Styles
  calorieGoalContainer: {
    backgroundColor: colors.surface,
    margin: spacing.md,
    padding: spacing.lg,
    borderRadius: borderRadius.medium,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  calorieGoalTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  calorieGoalSubtitle: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  calorieInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  calorieInput: {
    flex: 1,
    borderWidth: 2,
    borderColor: colors.border,
    borderRadius: borderRadius.small,
    padding: spacing.md,
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    textAlign: 'center',
    marginRight: spacing.sm,
    maxWidth: 120,
  },
  calorieUnit: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  calorieActions: {
    gap: spacing.sm,
  },
  submitCalorieButton: {
    backgroundColor: colors.primary,
    borderRadius: borderRadius.small,
    padding: spacing.md,
    alignItems: 'center',
  },
  submitCalorieButtonDisabled: {
    backgroundColor: colors.textSecondary,
  },
  submitCalorieButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
  },
  cancelCalorieButton: {
    borderWidth: 2,
    borderColor: colors.border,
    borderRadius: borderRadius.small,
    padding: spacing.md,
    alignItems: 'center',
  },
  cancelCalorieButtonText: {
    color: colors.textSecondary,
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
  },
});

export default AIChatScreen;
