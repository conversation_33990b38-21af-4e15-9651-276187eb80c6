// Test to verify admin filtering matches user management table logic
console.log('🔍 Testing Admin Verification Logic...\n');

// Simulate users from the user management table
const usersFromTable = [
  {
    _id: 'user1',
    username: 'admin',
    email: '<EMAIL>',
    isAdmin: true,
    isActive: true,
    isEmailVerified: true
  },
  {
    _id: 'user2', 
    username: 'superadmin',
    email: '<EMAIL>',
    isAdmin: true,
    isActive: true,
    isEmailVerified: true
  },
  {
    _id: 'user3',
    username: 'an<PERSON><PERSON>',
    email: '<EMAIL>',
    isAdmin: false, // Regular user - should NOT appear in recent activity
    isActive: true,
    isEmailVerified: true
  },
  {
    _id: 'user4',
    username: 'disabledadmin',
    email: '<EMAIL>',
    isAdmin: true,
    isActive: false, // Disabled admin - should NOT appear in recent activity
    isEmailVerified: true
  },
  {
    _id: 'user5',
    username: 'john',
    email: '<EMAIL>',
    isAdmin: false, // Regular user - should NOT appear in recent activity
    isActive: true,
    isEmailVerified: true
  }
];

console.log('👥 All users from User Management Table:');
usersFromTable.forEach((user, index) => {
  const adminStatus = user.isAdmin ? '👑 ADMIN' : '👤 USER';
  const activeStatus = user.isActive ? '✅ Active' : '❌ Disabled';
  console.log(`${index + 1}. ${user.username} (${user.email}) - ${adminStatus} - ${activeStatus}`);
});

// Apply our filtering logic
const activeAdmins = usersFromTable.filter(user => 
  user.isAdmin === true && user.isActive === true
);

console.log('\n👑 Users that SHOULD appear in Recent Admin Activity:');
activeAdmins.forEach((user, index) => {
  console.log(`${index + 1}. ${user.username} (${user.email}) - Admin: ${user.isAdmin}, Active: ${user.isActive}`);
});

console.log('\n🚫 Users that should NOT appear in Recent Admin Activity:');
const filteredOut = usersFromTable.filter(user => 
  !(user.isAdmin === true && user.isActive === true)
);
filteredOut.forEach((user, index) => {
  const reason = !user.isAdmin ? 'Not Admin' : !user.isActive ? 'Disabled Admin' : 'Unknown';
  console.log(`${index + 1}. ${user.username} (${user.email}) - Reason: ${reason}`);
});

// Simulate activities
const activities = [
  { user: 'user1', action: 'disabled_account', createdAt: new Date() },
  { user: 'user2', action: 'made_admin', createdAt: new Date() },
  { user: 'user3', action: 'login', createdAt: new Date() }, // Should be filtered out
  { user: 'user4', action: 'deleted_feedback', createdAt: new Date() }, // Should be filtered out
  { user: 'user5', action: 'submitted_feedback', createdAt: new Date() } // Should be filtered out
];

const adminUserIds = activeAdmins.map(user => user._id);
const adminActivities = activities.filter(activity => 
  adminUserIds.includes(activity.user)
);

console.log('\n📋 Activity Filtering Results:');
console.log(`Total activities: ${activities.length}`);
console.log(`Admin activities that will show: ${adminActivities.length}`);
console.log(`Activities filtered out: ${activities.length - adminActivities.length}`);

console.log('\n✅ Activities that WILL show in Recent Admin Activity:');
adminActivities.forEach((activity, index) => {
  const user = usersFromTable.find(u => u._id === activity.user);
  console.log(`${index + 1}. ${activity.action} by ${user.username} (${user.email})`);
});

console.log('\n❌ Activities that will NOT show (filtered out):');
const filteredActivities = activities.filter(activity => 
  !adminUserIds.includes(activity.user)
);
filteredActivities.forEach((activity, index) => {
  const user = usersFromTable.find(u => u._id === activity.user);
  const reason = !user.isAdmin ? 'User is not admin' : !user.isActive ? 'Admin is disabled' : 'Unknown';
  console.log(`${index + 1}. ${activity.action} by ${user.username} (${user.email}) - ${reason}`);
});

console.log('\n🎯 SUMMARY:');
console.log('✅ Only active admins from the User Management table will appear in Recent Admin Activity');
console.log('❌ Regular users like "anaskhan" will NOT appear');
console.log('❌ Disabled admins will NOT appear');
console.log('✅ This matches exactly with the User Management table logic');
