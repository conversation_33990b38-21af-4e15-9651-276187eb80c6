const axios = require('./PanlasApp Website/panlasapp web/meal-planner-backend/node_modules/axios');

// Test the refresh API endpoint
async function testRefreshAPI() {
  try {
    console.log('Testing refresh API endpoint...');
    
    // Test without authentication first
    console.log('\n1. Testing without authentication:');
    try {
      const response = await axios.get('http://localhost:5000/api/users/recently-viewed-meals');
      console.log('Response:', response.data);
    } catch (error) {
      console.log('Expected error (no auth):', error.response?.status, error.response?.data?.message);
    }
    
    // Test CORS endpoint
    console.log('\n2. Testing CORS endpoint:');
    try {
      const corsResponse = await axios.get('http://localhost:5000/api/cors-test');
      console.log('CORS test response:', corsResponse.data);
    } catch (error) {
      console.log('CORS test error:', error.message);
    }
    
    // Test health check
    console.log('\n3. Testing health check:');
    try {
      const healthResponse = await axios.get('http://localhost:5000/');
      console.log('Health check response:', healthResponse.data);
    } catch (error) {
      console.log('Health check error:', error.message);
    }
    
    console.log('\n✅ API tests completed');
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testRefreshAPI();
