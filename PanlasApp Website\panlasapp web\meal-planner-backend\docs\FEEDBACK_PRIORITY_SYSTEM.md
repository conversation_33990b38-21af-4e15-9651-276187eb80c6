# Feedback Priority Assignment System

## Overview
The feedback management system now automatically assigns priority levels to feedback submissions based on multiple factors including category, content analysis, and user ratings.

## Priority Levels
- **Low**: General suggestions, meal recommendations, positive feedback
- **Medium**: Feature requests, user experience feedback, general issues
- **High**: Bug reports, technical issues, performance problems
- **Urgent**: Critical issues, security concerns, payment problems, app crashes

## Priority Assignment Logic

### 1. Base Priority by Category
Each feedback category has a default priority level:

| Category | Base Priority |
|----------|---------------|
| Bug Report | High |
| Technical Issue | High |
| Feature Request | Medium |
| User Experience | Medium |
| Meal Suggestions | Low |
| General Feedback | Low |
| Other | Medium |

### 2. Content Analysis
The system analyzes the subject and message content for specific keywords:

#### Critical Keywords (Upgrade to Urgent)
- crash, error, broken, not working
- urgent, critical, emergency, severe
- security, data loss, major issue
- cannot login, payment, billing
- completely broken

#### High Priority Keywords (Upgrade to High)
- slow, performance, timeout, loading, freezing
- important, asap, soon, priority, issue

### 3. Rating Consideration
Low user ratings (1-2 stars) increase the priority:
- Low → Medium
- Medium → High  
- High → Urgent

## Examples

### Example 1: Critical Bug
- **Category**: Bug Report
- **Subject**: "App crashes on startup"
- **Message**: "The app crashes every time I try to open it"
- **Rating**: 1 star
- **Result**: Urgent (High base + crash keyword + low rating)

### Example 2: Feature Request
- **Category**: Feature Request
- **Subject**: "Add dark mode"
- **Message**: "Would love to have a dark mode option"
- **Rating**: 5 stars
- **Result**: Medium (Medium base, no keywords, good rating)

### Example 3: Performance Issue
- **Category**: General Feedback
- **Subject**: "App is slow"
- **Message**: "The app performance is really slow"
- **Rating**: 3 stars
- **Result**: High (Low base + performance keyword = High)

## Implementation Details

### Backend Changes
- Added `determinePriority()` function in `feedbackController.js`
- Automatic priority assignment during feedback submission
- Priority can still be manually updated by admins

### Frontend Changes
- Added priority filter in admin dashboard
- Priority displayed as colored tags
- Editable priority dropdown in feedback table
- Priority shown in feedback detail modal

### API Changes
- Priority included in feedback submission response
- Priority update endpoint accepts priority changes
- Priority filtering in feedback list endpoint

## Testing
Run the priority assignment tests:
```bash
node meal-planner-backend/tests/priorityAssignment.test.js
```

## Future Enhancements
- Machine learning-based priority prediction
- Integration with support ticket systems
- Automatic escalation for urgent items
- Priority-based notification system
