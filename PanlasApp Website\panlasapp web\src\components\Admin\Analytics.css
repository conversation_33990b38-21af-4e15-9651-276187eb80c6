.analytics-container {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.analytics-header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  width: 100%;
  max-width: 1000px;
}

.analytics-header h1 {
  color: #2c3e50;
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.analytics-header p {
  color: #6c757d;
  font-size: 1rem;
  margin-top: 0.3rem;
  line-height: 1.4;
  font-weight: 500;
}

/* Filters */
.analytics-filters {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  max-width: 1000px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.filter-group label {
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.9rem;
}

.filter-group select {
  padding: 0.7rem 0.9rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-group select:focus {
  outline: none;
  border-color: #20C5AF;
  box-shadow: 0 0 0 3px rgba(32, 197, 175, 0.15);
  transform: translateY(-1px);
}

/* Overview Stats */
.analytics-overview {
  margin-bottom: 2rem;
  width: 100%;
  max-width: 1000px;
}

.analytics-overview h2 {
  color: #2c3e50;
  margin-bottom: 1.2rem;
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
  position: relative;
}

.analytics-overview h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #20C5AF, #17a2b8);
  border-radius: 2px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.2rem;
  margin-bottom: 1.5rem;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 1.2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  min-height: 90px;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
}

.stat-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 12px 32px rgba(32, 197, 175, 0.15);
}

.stat-icon {
  width: 55px;
  height: 55px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: white;
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
  box-shadow: 0 4px 16px rgba(32, 197, 175, 0.3);
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.2rem 0;
  color: #2c3e50;
  line-height: 1.1;
}

.stat-content p {
  margin: 0;
  color: #6c757d;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Session Stats */
.session-stats {
  margin-bottom: 2rem;
  width: 100%;
  max-width: 1000px;
}

.session-stats h2 {
  color: #2c3e50;
  margin-bottom: 1.2rem;
  font-size: 1.7rem;
  text-align: center;
  position: relative;
}

.session-stats h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #20C5AF, #17a2b8);
  border-radius: 2px;
}

.session-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
}

.session-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.3rem;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
}

.session-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 10px 28px rgba(32, 197, 175, 0.15);
}

.session-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  box-shadow: 0 4px 12px rgba(32, 197, 175, 0.3);
}

.session-content h3 {
  font-size: 1.6rem;
  font-weight: 700;
  margin: 0 0 0.2rem 0;
  color: #2c3e50;
}

.session-content p {
  margin: 0 0 0.4rem 0;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.session-breakdown {
  display: flex;
  gap: 0.6rem;
  margin-top: 0.3rem;
}

.admin-count, .user-count {
  padding: 0.15rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.admin-count {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
}

.user-count {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #2563eb;
}

/* Platform Analytics */
.platform-analytics {
  margin-bottom: 2rem;
  width: 100%;
  max-width: 1000px;
}

.platform-analytics h2 {
  color: #2c3e50;
  margin-bottom: 1.2rem;
  font-size: 1.7rem;
  text-align: center;
  position: relative;
}

.platform-analytics h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #20C5AF, #17a2b8);
  border-radius: 2px;
}

.platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
}

.platform-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.3rem;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.platform-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #20C5AF, #17a2b8);
}

.platform-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 32px rgba(32, 197, 175, 0.15);
}

.platform-icon {
  width: 55px;
  height: 55px;
  border-radius: 14px;
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: white;
  margin: 0 auto 0.8rem;
  box-shadow: 0 4px 16px rgba(32, 197, 175, 0.3);
}

.platform-content h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.6rem;
  color: #2c3e50;
  text-transform: capitalize;
}

.platform-stats p {
  margin: 0.2rem 0;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.platform-breakdown {
  display: flex;
  justify-content: center;
  gap: 0.6rem;
  margin-top: 0.6rem;
}

.admin-events, .user-events {
  padding: 0.15rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.admin-events {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #dc2626;
}

.user-events {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #2563eb;
}

/* Event Analytics */
.event-analytics {
  margin-bottom: 2rem;
  width: 100%;
  max-width: 1000px;
}

.event-analytics h2 {
  color: #2c3e50;
  margin-bottom: 1.2rem;
  font-size: 1.7rem;
  text-align: center;
  position: relative;
}

.event-analytics h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #20C5AF, #17a2b8);
  border-radius: 2px;
}

.events-table {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin: 0 auto;
  max-width: 850px;
}

.events-table table {
  width: 100%;
  border-collapse: collapse;
}

.events-table th {
  background: linear-gradient(135deg, #20C5AF, #17a2b8);
  color: white;
  padding: 0.7rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.events-table td {
  padding: 0.7rem;
  border-bottom: 1px solid rgba(241, 243, 244, 0.8);
  font-size: 0.85rem;
  font-weight: 500;
}

.events-table tr:hover {
  background: rgba(32, 197, 175, 0.05);
  transform: scale(1.01);
  transition: all 0.2s ease;
}

.event-name {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Daily Trends */
.trends-analytics {
  margin-bottom: 2rem;
}

.trends-analytics h2 {
  color: #2c3e50;
  margin-bottom: 1.2rem;
  font-size: 1.8rem;
  text-align: center;
}

.trends-chart {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 0.8rem;
  overflow-x: auto;
  min-height: 180px;
  align-items: flex-end;
  justify-content: center;
  margin: 0 auto;
  max-width: 1000px;
}

.trend-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
}

.trend-date {
  font-size: 0.75rem;
  color: #7f8c8d;
  margin-bottom: 0.4rem;
  transform: rotate(-45deg);
  white-space: nowrap;
}

.trend-bars {
  display: flex;
  gap: 2px;
  height: 100px;
  align-items: flex-end;
  margin-bottom: 0.4rem;
}

.trend-bar {
  width: 12px;
  min-height: 4px;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.trend-bar.admin {
  background: linear-gradient(to top, #e74c3c, #c0392b);
}

.trend-bar.user {
  background: linear-gradient(to top, #20C5AF, #17a2b8);
}

.trend-total {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Loading and Error States */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 1rem;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: circleSpinner 1s linear infinite;
}

/* Use consistent circle loader */
.analytics-circle-loader {
  width: 50px;
  height: 50px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: circleSpinner 1s linear infinite;
}

@keyframes circleSpinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1rem;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-container {
    padding: 1rem;
  }

  .analytics-header h1 {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .analytics-filters {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .filter-group {
    min-width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .session-grid,
  .platform-grid {
    grid-template-columns: 1fr;
  }

  .trends-chart {
    padding: 1rem;
    gap: 0.5rem;
  }

  .trend-day {
    min-width: 60px;
  }

  .events-table {
    font-size: 0.8rem;
  }

  .events-table th,
  .events-table td {
    padding: 0.6rem;
  }
}
