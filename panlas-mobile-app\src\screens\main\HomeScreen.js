import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
  TextInput,
  ScrollView,
  RefreshControl,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { mealsAPI, userAPI, mealPlansAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { useFavorites } from '../../context/FavoritesContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius, legacyFonts, legacySpacing } from '../../styles/theme';
import { commonStyles, createResponsiveStyles } from '../../styles/commonStyles';
import { useResponsiveScreen, getResponsiveFontSize, getResponsiveSpacing, getResponsiveDimensions } from '../../utils/responsive';
import FloatingChatButton from '../../components/FloatingChatButton';

const HomeScreen = ({ navigation }) => {
  const [meals, setMeals] = useState([]);
  const [filteredMeals, setFilteredMeals] = useState([]);
  const [popularMeals, setPopularMeals] = useState([]);
  const [recommendedMeals, setRecommendedMeals] = useState([]);
  const [recentlyViewed, setRecentlyViewed] = useState([]);
  const [userPreferences, setUserPreferences] = useState({});
  const [familyMembers, setFamilyMembers] = useState([]);
  const [appliedFilters, setAppliedFilters] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('name');

  // Pagination state for performance optimization
  const [displayedMealsCount, setDisplayedMealsCount] = useState(20); // Show 20 meals initially
  const [isLoadingMore, setIsLoadingMore] = useState(false); // 'name', 'price-low', 'price-high'
  const [showSortModal, setShowSortModal] = useState(false);

  // Quick meal picker modal state
  const [showMealPicker, setShowMealPicker] = useState(false);
  const [selectedMealForPicker, setSelectedMealForPicker] = useState(null);
  const [selectedDates, setSelectedDates] = useState([]);
  const [selectedMealTypes, setSelectedMealTypes] = useState([]);
  const [isCreatingMealPlan, setIsCreatingMealPlan] = useState(false);

  const { user } = useAuth();
  const { favorites, isFavorite, addFavorite, removeFavorite } = useFavorites();
  const { size: screenSize, width: screenWidth } = useResponsiveScreen();
  const dimensions = getResponsiveDimensions(screenSize);
  const responsiveStyles = createResponsiveStyles(screenSize);
  const homeStyles = createHomeStyles(screenSize);

  const categories = ['All', 'Breakfast', 'Lunch', 'Dinner', 'Soup', 'Grilled', 'Meat', 'Vegetable', 'Rice Cake'];

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    filterMeals();
  }, [meals, searchQuery, selectedCategory, sortBy, userPreferences]);

  // Refresh recently viewed meals when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      loadRecentlyViewed();
    }, [])
  );

  const loadUserPreferences = async () => {
    try {
      if (user) {
        const response = await userAPI.getDietaryPreferences();
        console.log('Loaded user preferences:', response);

        if (response.data && response.data.success && response.data.dietaryPreferences) {
          console.log('Setting user preferences to (from data):', response.data.dietaryPreferences);
          setUserPreferences(response.data.dietaryPreferences);
        } else if (response.success && response.dietaryPreferences) {
          console.log('Setting user preferences to (direct):', response.dietaryPreferences);
          setUserPreferences(response.dietaryPreferences);
        } else {
          console.log('No dietary preferences found, setting empty object');
          setUserPreferences({});
        }
      } else {
        console.log('No user logged in, setting empty preferences');
        setUserPreferences({});
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      setUserPreferences({});
    }
  };

  const loadFamilyMembers = async () => {
    try {
      if (user) {
        const response = await userAPI.getFamilyMembers();
        setFamilyMembers(response.data?.familyMembers || []);
      }
    } catch (error) {
      console.error('Error loading family members:', error);
      setFamilyMembers([]);
    }
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return null;

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Get age-based color for family member cards
  const getAgeBasedColor = (dateOfBirth) => {
    const age = calculateAge(dateOfBirth);

    if (age === null) {
      return '#8E8E93'; // Default color for unknown age (iOS gray)
    }

    if (age < 13) {
      return '#FF3B30'; // Vibrant red for children (0-12)
    } else if (age < 20) {
      return '#30D158'; // Vibrant green for teenagers (13-19)
    } else if (age < 35) {
      return '#007AFF'; // Vibrant blue for young adults (20-34)
    } else if (age < 55) {
      return '#FF9500'; // Vibrant orange for adults (35-54)
    } else {
      return '#AF52DE'; // Vibrant purple for seniors (55+)
    }
  };

  const loadInitialData = async () => {
    try {
      setLoading(true);
      // Load user preferences first, then meals so filtering works correctly
      await loadUserPreferences();
      await loadFamilyMembers();
      await loadMeals();
      await loadPopularMeals();
      await loadRecommendedMeals();
      await loadRecentlyViewed();
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMeals = async () => {
    try {
      const response = await mealsAPI.getFilipinoDishes();
      const mealsData = response?.data || [];

      // Ensure mealsData is an array
      if (Array.isArray(mealsData)) {
        setMeals(mealsData);
        setFilteredMeals(mealsData);
      } else {
        console.warn('Meals response is not an array:', mealsData);
        setMeals([]);
        setFilteredMeals([]);
      }
    } catch (error) {
      console.error('Error loading meals:', error);
      setMeals([]);
      setFilteredMeals([]);
      Alert.alert('Error', 'Failed to load meals. Please check your internet connection.');
    }
  };

  const loadPopularMeals = async () => {
    try {
      const response = await mealsAPI.getPopularMeals();
      const meals = response?.data || [];

      // Ensure meals is an array
      if (Array.isArray(meals)) {
        setPopularMeals(meals);
      } else {
        console.warn('Popular meals response is not an array:', meals);
        setPopularMeals([]);
      }
    } catch (error) {
      console.error('Error loading popular meals:', error);
      setPopularMeals([]);
    }
  };

  const loadRecommendedMeals = async () => {
    try {
      if (user) {
        // Get recommendations based on user + family dietary preferences
        const response = await mealsAPI.getMealRecommendations({
          includeFamily: 'true'
        });
        const recommendedMeals = response?.data || [];

        // Store applied filters for display
        setAppliedFilters(response?.appliedFilters || {});

        // Ensure recommendedMeals is an array and limit to 5 items
        if (Array.isArray(recommendedMeals)) {
          setRecommendedMeals(recommendedMeals.slice(0, 5));
        } else {
          console.warn('Recommended meals response is not an array:', recommendedMeals);
          setRecommendedMeals([]);
        }
      } else {
        // For non-logged in users, use popular meals as recommendations
        const response = await mealsAPI.getPopularMeals();
        const allMeals = response?.data || [];

        if (Array.isArray(allMeals)) {
          setRecommendedMeals(allMeals.slice(0, 5));
        } else {
          console.warn('Popular meals response is not an array:', allMeals);
          setRecommendedMeals([]);
        }
      }
    } catch (error) {
      console.error('Error loading recommended meals:', error);
      // Fallback to popular meals if recommendations fail
      try {
        const response = await mealsAPI.getPopularMeals();
        const allMeals = response?.data || [];
        if (Array.isArray(allMeals)) {
          setRecommendedMeals(allMeals.slice(0, 5));
        } else {
          setRecommendedMeals([]);
        }
      } catch (fallbackError) {
        console.error('Error loading fallback meals:', fallbackError);
        setRecommendedMeals([]);
      }
    }
  };

  const loadRecentlyViewed = async () => {
    try {
      console.log('=== LOADING RECENTLY VIEWED MEALS (HOME SCREEN) ===');
      const response = await userAPI.getRecentlyViewedMeals();
      console.log('HomeScreen API response:', JSON.stringify(response, null, 2));

      // Handle different response structures
      let meals = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          meals = response.data;
          console.log('Response data is array, using directly');
        } else if (response.data.recentlyViewedMeals) {
          meals = response.data.recentlyViewedMeals;
          console.log('Response data has recentlyViewedMeals property');
        } else {
          console.log('Response data structure not recognized:', response.data);
        }
      } else {
        console.log('No response.data found');
      }

      console.log('Setting recently viewed meals count:', meals.length);
      setRecentlyViewed(meals);
    } catch (error) {
      console.error('Error loading recently viewed meals:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
    }
  };

  const getPriceValue = (meal) => {
    // Use actual price field from the meal data
    return meal.price || 0;
  };

  const filterMeals = () => {
    let filtered = meals;

    console.log('🔍 Filtering meals with user preferences:', userPreferences);
    console.log('🔍 Filtering meals with family members:', familyMembers);
    console.log('📊 Total meals before filtering:', meals.length);

    // Combine user and family dietary preferences
    const allRestrictions = [...(userPreferences?.restrictions || [])];
    const allAllergies = [...(userPreferences?.allergies || [])];

    // Add family member preferences if they exist
    if (familyMembers && familyMembers.length > 0) {
      familyMembers.forEach(member => {
        if (member.dietaryPreferences) {
          allRestrictions.push(...(member.dietaryPreferences.restrictions || []));
          allAllergies.push(...(member.dietaryPreferences.allergies || []));
        }
      });
    }

    // Remove duplicates
    const uniqueRestrictions = [...new Set(allRestrictions)];
    const uniqueAllergies = [...new Set(allAllergies)];

    console.log('🔍 Combined restrictions:', uniqueRestrictions);
    console.log('🔍 Combined allergies:', uniqueAllergies);

    // Only apply filtering if there are actual preferences to filter by
    if (uniqueRestrictions.length === 0 && uniqueAllergies.length === 0) {
      console.log('ℹ️ No dietary preferences found, showing all meals');
    } else {
      filtered = filtered.filter(meal => {
        const dietType = meal.dietType || meal.dietaryAttributes || {};

        // Check dietary restrictions using combined preferences
        if (uniqueRestrictions.length > 0) {
          const meetsRestrictions = uniqueRestrictions.every(restriction => {
            let meets = false;
            switch(restriction) {
              case 'Vegetarian':
                meets = dietType.isVegetarian;
                break;
              case 'Vegan':
                meets = dietType.isVegan;
                break;
              case 'Gluten-Free':
                meets = dietType.isGlutenFree;
                break;
              case 'Dairy-Free':
                meets = dietType.isDairyFree;
                break;
              case 'Nut-Free':
                meets = dietType.isNutFree;
                break;
              case 'Low-Carb':
                meets = dietType.isLowCarb;
                break;
              case 'Keto':
                meets = dietType.isKeto;
                break;
              case 'Pescatarian':
                meets = dietType.isPescatarian;
                break;
              case 'Halal':
                meets = dietType.isHalal;
                break;
              default:
                meets = true;
            }

            return meets;
          });
          if (!meetsRestrictions) return false;
        }

        // Check allergies - exclude meals with allergens using combined preferences
        if (uniqueAllergies.length > 0) {
          const ingredients = meal.ingredients || [];
          const hasAllergen = uniqueAllergies.some(allergy =>
            ingredients.some(ingredient =>
              ingredient.toLowerCase().includes(allergy.toLowerCase())
            )
          );
          if (hasAllergen) {
            console.log(`❌ Meal "${meal.name}" contains allergen "${uniqueAllergies.join(', ')}" in ingredients: ${ingredients.join(', ')}`);
            return false;
          }
        }

        // Note: Calorie filtering removed to maintain consistency with backend and CreateMealPlan
        // Users can filter by calories manually if needed

        return true;
      });
    }

    // Filter by category/meal type
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(meal => {
        // Check if it's a meal type filter (Breakfast, Lunch, Dinner)
        if (['Breakfast', 'Lunch', 'Dinner'].includes(selectedCategory)) {
          // Use mealType field for meal time filtering
          if (Array.isArray(meal.mealType)) {
            return meal.mealType.some(type =>
              type.toLowerCase() === selectedCategory.toLowerCase()
            );
          } else if (meal.mealType) {
            return meal.mealType.toLowerCase() === selectedCategory.toLowerCase();
          }
          return false;
        } else {
          // Use category field for other filters (Soup, Grilled, etc.)
          if (Array.isArray(meal.category)) {
            return meal.category.some(cat =>
              cat.toLowerCase() === selectedCategory.toLowerCase()
            );
          } else if (meal.category) {
            return meal.category.toLowerCase() === selectedCategory.toLowerCase();
          }
          return false;
        }
      });
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (meal.description && meal.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Sort meals
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return getPriceValue(a) - getPriceValue(b);
        case 'price-high':
          return getPriceValue(b) - getPriceValue(a);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    console.log('✅ Meals after filtering:', filtered.length);
    if (filtered.length > 0) {
      console.log('📋 Sample filtered meals:', filtered.slice(0, 3).map(m => m.name));
    }

    setFilteredMeals(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const handleMealPress = (meal) => {
    // Navigate to meal detail - tracking will be handled by MealDetailScreen
    navigation.navigate('MealDetail', { meal });
  };

  const handleFavoritePress = async (meal) => {
    try {
      const mealId = meal.id || meal._id;
      if (isFavorite(mealId)) {
        await removeFavorite(mealId);
      } else {
        await addFavorite(meal);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update favorites');
    }
  };

  const handleAddToMealPlan = (meal) => {
    setSelectedMealForPicker(meal);
    setSelectedDates([]);
    setSelectedMealTypes([]);
    setShowMealPicker(true);
  };

  const toggleDateSelection = (date) => {
    setSelectedDates(prev =>
      prev.includes(date)
        ? prev.filter(d => d !== date)
        : [...prev, date]
    );
  };

  const toggleMealTypeSelection = (mealType) => {
    setSelectedMealTypes(prev =>
      prev.includes(mealType)
        ? prev.filter(mt => mt !== mealType)
        : [...prev, mealType]
    );
  };

  // Helper function to get valid meal ID for backend
  const getMealIdForBackend = (meal) => {
    // The backend meals array expects ObjectId references
    // Prioritize _id over id, and ensure it's a valid MongoDB ObjectId
    const mealId = meal._id;

    if (!mealId) {
      console.error('No _id found in meal object:', meal);
      throw new Error('Meal object must have a valid _id property');
    }

    // Ensure it's a valid ObjectId format (24 character hex string)
    if (typeof mealId !== 'string' || mealId.length !== 24) {
      console.error('Invalid ObjectId format:', mealId);
      throw new Error('Meal _id must be a valid 24-character ObjectId');
    }

    console.log('Meal ID being sent:', mealId, 'Type:', typeof mealId, 'Original meal:', meal.name);
    return mealId;
  };

  const handleCreateMealPlans = async () => {
    if (selectedDates.length === 0 || selectedMealTypes.length === 0) {
      Alert.alert('Selection Required', 'Please select at least one date and one meal type.');
      return;
    }

    try {
      setIsCreatingMealPlan(true);
      const meal = selectedMealForPicker;

      // Create meal plans for each combination of date and meal type
      const promises = [];
      for (const date of selectedDates) {
        for (const mealType of selectedMealTypes) {
          const mealId = getMealIdForBackend(meal);

          promises.push(
            mealPlansAPI.createOrUpdateMealPlan({
              date: date,
              mealType: mealType,
              meal: mealId
            })
          );

          // Also track for history
          promises.push(
            userAPI.addRecentlyAddedToMealPlan({
              meal,
              addedToDate: date,
              addedToMealType: mealType
            })
          );
        }
      }

      await Promise.all(promises);

      // Close modal and reset state
      setShowMealPicker(false);
      setSelectedMealForPicker(null);
      setSelectedDates([]);
      setSelectedMealTypes([]);

      // Show success message
      const dateText = selectedDates.length === 1 ? selectedDates[0] : `${selectedDates.length} dates`;
      const mealTypeText = selectedMealTypes.length === 1 ? selectedMealTypes[0] : `${selectedMealTypes.length} meal types`;

      Alert.alert(
        'Success!',
        `${meal.name} has been added to ${mealTypeText} for ${dateText}`,
        [
          {
            text: 'View Meal Plans',
            onPress: () => navigation.navigate('MealPlans')
          },
          { text: 'OK' }
        ]
      );
    } catch (error) {
      console.error('Error creating meal plans:', error);
      Alert.alert('Error', 'Failed to create meal plans. Please try again.');
    } finally {
      setIsCreatingMealPlan(false);
    }
  };

  const getPriceIndicator = (meal) => {
    // Show actual price if available, otherwise show price category
    if (meal.price && meal.price > 0) {
      return `₱${meal.price}`;
    }

    // Fallback to price category based on calories and complexity
    const calories = meal.calories || 0;
    const hasComplexIngredients = meal.ingredients && meal.ingredients.length > 8;

    if (calories < 200 && !hasComplexIngredients) {
      return '₱50-150'; // Low Range
    } else if (calories < 400 || hasComplexIngredients) {
      return '₱150-300'; // Mid Range
    } else {
      return '₱300+'; // High Range
    }
  };

  // Optimized meal card component with React.useCallback
  const renderMealCard = React.useCallback(({ item: meal }) => (
    <TouchableOpacity
      style={commonStyles.foodCard}
      onPress={() => handleMealPress(meal)}
    >
      <View style={commonStyles.foodCardImage}>
        <Image
          source={{ uri: meal.image || 'https://via.placeholder.com/300x200' }}
          style={styles.mealImage}
          resizeMode="cover"
        />
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => handleFavoritePress(meal)}
        >
          <Ionicons
            name={isFavorite(meal.id || meal._id) ? "heart" : "heart-outline"}
            size={24}
            color={isFavorite(meal.id || meal._id) ? colors.secondary : colors.surface}
          />
        </TouchableOpacity>
      </View>
      <View style={commonStyles.foodCardContent}>
        <Text style={commonStyles.foodCardTitle} numberOfLines={2}>
          {meal.name}
        </Text>
        <View style={commonStyles.foodCardMeta}>
          <View style={commonStyles.categoryTag}>
            <Text style={commonStyles.categoryTagText}>
              {Array.isArray(meal.category) ? meal.category[0] : meal.category}
            </Text>
          </View>
          <View style={styles.priceIndicator}>
            <Text style={styles.priceText}>{getPriceIndicator(meal)}</Text>
          </View>
          <View style={commonStyles.rating}>
            <Ionicons name="star" size={16} color="#FFD700" />
            <Text style={commonStyles.ratingText}>
              {(meal.rating && meal.rating > 0) ? meal.rating : '4.5'}
            </Text>
          </View>
        </View>
        {meal.description && (
          <Text style={styles.mealDescription} numberOfLines={2}>
            {meal.description}
          </Text>
        )}

        {/* Meal Tags */}
        {(meal.tags || meal.dietaryTags || meal.mealType) && (
          <View style={styles.mealTagsContainer}>
            {/* Show first 3 tags from various sources */}
            {[
              ...(meal.mealType || []).map(tag => ({ text: tag, type: 'mealType' })),
              ...(meal.dietaryTags || []).map(tag => ({ text: tag, type: 'dietary' })),
              ...(meal.tags || []).map(tag => ({ text: tag, type: 'general' }))
            ].slice(0, 3).map((tag, index) => (
              <View key={index} style={[styles.mealTag, styles[`${tag.type}Tag`]]}>
                <Text style={styles.mealTagText}>{tag.text}</Text>
              </View>
            ))}
          </View>
        )}

        <View style={styles.mealActions}>
          <TouchableOpacity
            style={styles.addToPlanButton}
            onPress={() => handleAddToMealPlan(meal)}
          >
            <Ionicons name="calendar-outline" size={16} color={colors.primary} />
            <Text style={styles.addToPlanText}>Add to Meal</Text>
          </TouchableOpacity>
          {meal.calories && meal.calories > 0 && (
            <View style={styles.caloriesInfo}>
              <Text style={styles.caloriesText}>{meal.calories} cal</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  ), [favorites, handleMealPress, handleFavoritePress]);

  // Function to load more meals for infinite scrolling
  const loadMoreMeals = () => {
    if (isLoadingMore || displayedMealsCount >= filteredMeals.length) return;

    setIsLoadingMore(true);
    setTimeout(() => {
      setDisplayedMealsCount(prev => prev + 20); // Load 20 more meals
      setIsLoadingMore(false);
    }, 500); // Small delay to show loading state
  };

  // Get paginated meals for display
  const getDisplayedMeals = () => {
    return filteredMeals.slice(0, displayedMealsCount);
  };

  // Reset pagination when filters change
  useEffect(() => {
    setDisplayedMealsCount(20);
  }, [searchQuery, selectedCategory]);

  const renderCategoryFilter = ({ item: category }) => (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        selectedCategory === category && styles.categoryButtonActive
      ]}
      onPress={() => setSelectedCategory(category)}
    >
      <Text style={[
        styles.categoryButtonText,
        selectedCategory === category && styles.categoryButtonTextActive
      ]}>
        {category}
      </Text>
    </TouchableOpacity>
  );

  const renderPopularMeal = ({ item: meal }) => (
    <TouchableOpacity
      style={styles.popularMealCard}
      onPress={() => handleMealPress(meal)}
    >
      <Image
        source={{ uri: meal.image || 'https://via.placeholder.com/150x100' }}
        style={styles.popularMealImage}
        resizeMode="cover"
      />
      <Text style={styles.popularMealName} numberOfLines={2}>
        {meal.name}
      </Text>
    </TouchableOpacity>
  );

  // Quick Meal Picker Modal Component
  const renderQuickMealPicker = () => {
    if (!selectedMealForPicker) return null;

    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

    const formatDate = (date) => date.toISOString().split('T')[0];
    const formatDisplayDate = (date) => {
      const options = { weekday: 'short', month: 'short', day: 'numeric' };
      return date.toLocaleDateString('en-US', options);
    };

    const mealTypes = [
      { key: 'breakfast', label: 'Breakfast', icon: 'sunny-outline' },
      { key: 'lunch', label: 'Lunch', icon: 'partly-sunny-outline' },
      { key: 'dinner', label: 'Dinner', icon: 'moon-outline' },
      { key: 'snack', label: 'Snack', icon: 'cafe-outline' }
    ];

    const dates = [
      { key: formatDate(today), label: `Today (${formatDisplayDate(today)})` },
      { key: formatDate(tomorrow), label: `Tomorrow (${formatDisplayDate(tomorrow)})` },
      { key: formatDate(dayAfterTomorrow), label: formatDisplayDate(dayAfterTomorrow) }
    ];

    const canCreate = selectedDates.length > 0 && selectedMealTypes.length > 0;

    return (
      <Modal
        visible={showMealPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMealPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add to Meal Plan</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowMealPicker(false)}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.modalScrollView}
              showsVerticalScrollIndicator={false}
              bounces={false}
            >

            {/* Meal Info */}
            <View style={styles.modalMealInfo}>
              <Image
                source={{ uri: selectedMealForPicker.image || 'https://via.placeholder.com/80x60' }}
                style={styles.modalMealImage}
                resizeMode="cover"
              />
              <View style={styles.modalMealDetails}>
                <Text style={styles.modalMealName} numberOfLines={2}>
                  {selectedMealForPicker.name}
                </Text>
                <Text style={styles.modalMealCalories}>
                  {selectedMealForPicker.calories || 0} calories
                </Text>
              </View>
            </View>

            {/* Date Selection */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>
                Select Dates ({selectedDates.length} selected)
              </Text>
              <View style={styles.modalOptionsGrid}>
                {dates.map((date) => {
                  const isSelected = selectedDates.includes(date.key);
                  return (
                    <TouchableOpacity
                      key={date.key}
                      style={[
                        styles.modalDateOption,
                        isSelected && styles.modalOptionSelected
                      ]}
                      onPress={() => toggleDateSelection(date.key)}
                    >
                      <Ionicons
                        name={isSelected ? "calendar" : "calendar-outline"}
                        size={20}
                        color={isSelected ? colors.surface : colors.primary}
                      />
                      <Text style={[
                        styles.modalOptionText,
                        isSelected && styles.modalOptionTextSelected
                      ]}>
                        {date.label}
                      </Text>
                      {isSelected && (
                        <Ionicons
                          name="checkmark-circle"
                          size={16}
                          color={colors.surface}
                          style={styles.modalCheckmark}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Meal Type Selection */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>
                Select Meal Types ({selectedMealTypes.length} selected)
              </Text>
              <View style={styles.modalOptionsGrid}>
                {mealTypes.map((mealType) => {
                  const isSelected = selectedMealTypes.includes(mealType.key);
                  return (
                    <TouchableOpacity
                      key={mealType.key}
                      style={[
                        styles.modalMealTypeOption,
                        isSelected && styles.modalOptionSelected
                      ]}
                      onPress={() => toggleMealTypeSelection(mealType.key)}
                    >
                      <Ionicons
                        name={mealType.icon}
                        size={24}
                        color={isSelected ? colors.surface : colors.primary}
                      />
                      <Text style={[
                        styles.modalOptionText,
                        isSelected && styles.modalOptionTextSelected
                      ]}>
                        {mealType.label}
                      </Text>
                      {isSelected && (
                        <Ionicons
                          name="checkmark-circle"
                          size={16}
                          color={colors.surface}
                          style={styles.modalCheckmark}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.modalActions}>
              {/* Create Meal Plans Button */}
              <TouchableOpacity
                style={[
                  styles.modalCreateButton,
                  !canCreate && styles.modalCreateButtonDisabled
                ]}
                onPress={handleCreateMealPlans}
                disabled={!canCreate || isCreatingMealPlan}
              >
                {isCreatingMealPlan ? (
                  <ActivityIndicator size="small" color={colors.surface} />
                ) : (
                  <>
                    <Ionicons name="add-circle" size={20} color={colors.surface} />
                    <Text style={styles.modalCreateButtonText}>
                      Create Meal Plans
                    </Text>
                  </>
                )}
              </TouchableOpacity>

              {/* Advanced Options */}
              <TouchableOpacity
                style={styles.modalAdvancedButton}
                onPress={() => {
                  setShowMealPicker(false);
                  setSelectedMealForPicker(null);
                  setSelectedDates([]);
                  setSelectedMealTypes([]);
                  navigation.navigate('MealPlans', {
                    screen: 'CreateMealPlan',
                    params: { selectedMeal: selectedMealForPicker }
                  });
                }}
              >
                <Ionicons name="settings-outline" size={16} color={colors.primary} />
                <Text style={styles.modalAdvancedText}>Advanced Options</Text>
              </TouchableOpacity>
            </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={commonStyles.container}>
        <View style={commonStyles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={commonStyles.loadingText}>Loading Filipino meals...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={homeStyles.header}>
        <View style={homeStyles.headerLeft}>
          <Text style={homeStyles.greeting}>Hello, {user?.firstName || 'User'}!</Text>
          <Text style={homeStyles.subGreeting}>What would you like to cook today?</Text>
        </View>
      </View>

      {/* Search Bar - moved outside FlatList to prevent focus loss */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search Filipino dishes..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={colors.textSecondary}
            autoCorrect={false}
            autoCapitalize="none"
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => setShowSortModal(true)}
        >
          <Ionicons name="funnel-outline" size={20} color={colors.primary} />
          <Text style={styles.sortButtonText}>Sort</Text>
        </TouchableOpacity>
      </View>

      {/* Main Content with FlatList to avoid VirtualizedList nesting */}
      <FlatList
        data={getDisplayedMeals()}
        renderItem={renderMealCard}
        keyExtractor={(item) => item.id || item._id}
        numColumns={2}
        columnWrapperStyle={styles.mealRow}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        onEndReached={loadMoreMeals}
        onEndReachedThreshold={0.5}
        removeClippedSubviews={false}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={20}
        windowSize={10}
        ListHeaderComponent={() => (
          <View>

            {/* Family Dietary Preferences Display */}
            {familyMembers.length > 0 && (
              <View style={styles.familyPreferencesSection}>
                <View style={styles.familyPreferencesHeader}>
                  <Text style={styles.sectionTitle}>Family Dietary Preferences</Text>
                  <TouchableOpacity
                    style={styles.manageFamilyButton}
                    onPress={() => navigation.navigate('Family')}
                  >
                    <Text style={styles.manageFamilyButtonText}>Manage</Text>
                  </TouchableOpacity>
                </View>
                <FlatList
                  data={familyMembers}
                  renderItem={({ item: member }) => (
                    <View style={[
                      styles.familyMemberCard,
                      { borderLeftColor: getAgeBasedColor(member.dateOfBirth), borderLeftWidth: 4 }
                    ]}>
                      <View style={styles.familyMemberInfo}>
                        <View style={styles.familyMemberHeader}>
                          <Text style={styles.familyMemberName}>{member.name}</Text>
                          {member.dateOfBirth && calculateAge(member.dateOfBirth) && (
                            <Text style={styles.familyMemberAge}>
                              Age: {calculateAge(member.dateOfBirth)}
                            </Text>
                          )}
                        </View>
                        <View style={styles.familyMemberPreferences}>
                          {member.dietaryPreferences?.restrictions?.length > 0 || member.dietaryPreferences?.allergies?.length > 0 ? (
                            <View style={styles.preferencesBreakdown}>
                              {member.dietaryPreferences?.restrictions?.length > 0 && (
                                <View style={styles.preferenceItem}>
                                  <Text style={styles.preferenceLabel}>Diet: </Text>
                                  <Text style={styles.preferenceValues}>{member.dietaryPreferences.restrictions.join(', ')}</Text>
                                </View>
                              )}
                              {member.dietaryPreferences?.allergies?.length > 0 && (
                                <View style={styles.preferenceItem}>
                                  <Text style={styles.preferenceLabel}>Allergies: </Text>
                                  <Text style={styles.preferenceValues}>{member.dietaryPreferences.allergies.join(', ')}</Text>
                                </View>
                              )}
                            </View>
                          ) : (
                            <Text style={styles.noPreferences}>No dietary preferences set</Text>
                          )}
                        </View>
                      </View>
                    </View>
                  )}
                  keyExtractor={(item) => item._id || item.id}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.familyMembersContainer}
                />
              </View>
            )}

            {/* Category Filters */}
            <View style={styles.categoriesSection}>
              <Text style={styles.sectionTitle}>Categories</Text>
              <FlatList
                data={categories}
                renderItem={renderCategoryFilter}
                keyExtractor={(item) => item}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.categoriesContainer}
              />
            </View>

            {/* Recommended Meals */}
            {user && recommendedMeals.length > 0 && (
              <View style={styles.recommendedSection}>
                <Text style={styles.sectionTitle}>Recommended for Your Family</Text>

                {/* Applied Filters Display */}
                {appliedFilters && (appliedFilters.restrictions?.length > 0 || appliedFilters.allergies?.length > 0) && (
                  <View style={styles.appliedFiltersContainer}>
                    <Text style={styles.appliedFiltersTitle}>Based on family preferences:</Text>
                    <View style={styles.filtersRow}>
                      {appliedFilters.restrictions?.length > 0 && (
                        <View style={styles.filterTag}>
                          <Ionicons name="leaf-outline" size={14} color="#4CAF50" />
                          <Text style={styles.filterTagText}>
                            {appliedFilters.restrictions.join(', ')}
                          </Text>
                        </View>
                      )}
                      {appliedFilters.allergies?.length > 0 && (
                        <View style={[styles.filterTag, styles.allergyTag]}>
                          <Ionicons name="warning-outline" size={14} color="#FF9800" />
                          <Text style={[styles.filterTagText, styles.allergyTagText]}>
                            Avoiding: {appliedFilters.allergies.join(', ')}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                )}

                <FlatList
                  data={recommendedMeals}
                  renderItem={renderPopularMeal}
                  keyExtractor={(item) => (item.id || item._id).toString()}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.popularContainer}
                />
              </View>
            )}

            {/* Popular Meals */}
            {popularMeals.length > 0 && (
              <View style={styles.popularSection}>
                <Text style={styles.sectionTitle}>Popular Dishes</Text>
                <FlatList
                  data={popularMeals}
                  renderItem={renderPopularMeal}
                  keyExtractor={(item) => (item.id || item._id).toString()}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.popularContainer}
                />
              </View>
            )}

            {/* Recently Viewed */}
            {recentlyViewed.length > 0 && (
              <View style={styles.recentSection}>
                <Text style={styles.sectionTitle}>Recently Viewed</Text>
                <FlatList
                  data={recentlyViewed.slice(0, 5)}
                  renderItem={renderPopularMeal}
                  keyExtractor={(item) => (item.id || item._id).toString()}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.popularContainer}
                />
              </View>
            )}

            {/* All Meals Section Header */}
            <View style={styles.allMealsSection}>
              <Text style={styles.sectionTitle}>
                {searchQuery || selectedCategory !== 'All'
                  ? `${filteredMeals.length} Results`
                  : 'All Filipino Dishes'
                }
              </Text>
            </View>
          </View>
        )}
        ListFooterComponent={() => (
          isLoadingMore && displayedMealsCount < filteredMeals.length ? (
            <View style={styles.loadingMore}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingMoreText}>Loading more meals...</Text>
            </View>
          ) : displayedMealsCount < filteredMeals.length ? (
            <TouchableOpacity style={styles.loadMoreButton} onPress={loadMoreMeals}>
              <Text style={styles.loadMoreButtonText}>Load More Meals</Text>
            </TouchableOpacity>
          ) : null
        )}
        ListEmptyComponent={() => (
          <View style={commonStyles.emptyContainer}>
            <Ionicons name="restaurant-outline" size={64} color={colors.textSecondary} />
            <Text style={commonStyles.emptyTitle}>No meals found</Text>
            <Text style={commonStyles.emptySubtitle}>
              Try adjusting your search or category filter
            </Text>
            <TouchableOpacity
              style={commonStyles.primaryButton}
              onPress={() => {
                setSearchQuery('');
                setSelectedCategory('All');
              }}
            >
              <Text style={commonStyles.primaryButtonText}>Clear Filters</Text>
            </TouchableOpacity>
          </View>
        )}
      />

      {/* Quick Meal Picker Modal */}
      {renderQuickMealPicker()}

      {/* Sort Modal */}
      <Modal
        visible={showSortModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSortModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.sortModalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Sort by</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowSortModal(false)}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.sortOptionsContainer}>
              {[
                { key: 'name', label: 'Name (A-Z)', icon: 'text-outline' },
                { key: 'price-low', label: 'Price (Low to High)', icon: 'arrow-up-outline' },
                { key: 'price-high', label: 'Price (High to Low)', icon: 'arrow-down-outline' }
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.sortOption,
                    sortBy === option.key && styles.sortOptionSelected
                  ]}
                  onPress={() => {
                    setSortBy(option.key);
                    setShowSortModal(false);
                  }}
                >
                  <Ionicons
                    name={option.icon}
                    size={20}
                    color={sortBy === option.key ? colors.surface : colors.text}
                  />
                  <Text style={[
                    styles.sortOptionText,
                    sortBy === option.key && styles.sortOptionTextSelected
                  ]}>
                    {option.label}
                  </Text>
                  {sortBy === option.key && (
                    <Ionicons name="checkmark" size={20} color={colors.surface} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>

      {/* Floating Chat Button */}
      <FloatingChatButton />
    </SafeAreaView>
  );
};

// Create responsive styles function
const createHomeStyles = (screenSize) => {
  const responsiveDimensions = getResponsiveDimensions(screenSize);

  return StyleSheet.create({
    header: {
      backgroundColor: colors.surface,
      paddingHorizontal: getResponsiveSpacing('md', screenSize),
      paddingTop: getResponsiveSpacing('lg', screenSize),
      paddingBottom: getResponsiveSpacing('md', screenSize),
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      minHeight: responsiveDimensions.headerHeight,
    },
    headerLeft: {
      flex: 1,
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerButton: {
      padding: getResponsiveSpacing('sm', screenSize),
      marginLeft: getResponsiveSpacing('sm', screenSize),
      borderRadius: responsiveDimensions.borderRadius,
      minWidth: responsiveDimensions.buttonHeight,
      minHeight: responsiveDimensions.buttonHeight,
      alignItems: 'center',
      justifyContent: 'center',
    },
    greeting: {
      fontSize: getResponsiveFontSize('large', screenSize),
      fontWeight: 'bold',
      color: colors.text,
    },
    subGreeting: {
      fontSize: getResponsiveFontSize('medium', screenSize),
      color: colors.textSecondary,
      marginTop: getResponsiveSpacing('xs', screenSize),
    },
  });
};

const styles = StyleSheet.create({
  searchContainer: {
    paddingHorizontal: legacySpacing.md,
    paddingVertical: legacySpacing.md,
    backgroundColor: colors.surface,
    flexDirection: 'row',
    alignItems: 'center',
    gap: legacySpacing.sm,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    paddingHorizontal: legacySpacing.md,
    paddingVertical: legacySpacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: legacyFonts.sizes.medium,
    color: colors.text,
    marginLeft: legacySpacing.sm,
  },

  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: legacySpacing.md,
    paddingVertical: legacySpacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  sortButtonText: {
    fontSize: legacyFonts.sizes.small,
    color: colors.primary,
    marginLeft: legacySpacing.xs,
    fontWeight: '500',
  },
  categoriesSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
  },
  sectionTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md,
  },
  categoriesContainer: {
    paddingHorizontal: spacing.md,
  },
  categoryButton: {
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  categoryButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  categoryButtonTextActive: {
    color: colors.surface,
  },
  recommendedSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
  },
  popularSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
  },
  recentSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
  },
  popularContainer: {
    paddingHorizontal: spacing.md,
  },
  popularMealCard: {
    width: 150,
    marginRight: spacing.md,
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    overflow: 'hidden',
  },
  popularMealImage: {
    width: '100%',
    height: 100,
  },
  popularMealName: {
    fontSize: fonts.sizes.small,
    fontWeight: '500',
    color: colors.text,
    padding: spacing.sm,
  },
  allMealsSection: {
    paddingVertical: spacing.md,
    backgroundColor: colors.surface,
    marginTop: spacing.sm,
    paddingBottom: spacing.xxl,
  },
  mealsGrid: {
    paddingHorizontal: spacing.md,
  },
  mealCardWrapper: {
    marginBottom: spacing.md,
  },
  mealImage: {
    width: '100%',
    height: 200,
  },
  favoriteButton: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: borderRadius.round,
    padding: spacing.sm,
  },
  mealDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    lineHeight: 18,
  },
  mealActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.md,
  },
  addToPlanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  addToPlanText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  priceIndicator: {
    backgroundColor: colors.secondary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    marginHorizontal: spacing.xs,
  },
  priceText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    fontWeight: 'bold',
  },
  caloriesInfo: {
    alignItems: 'flex-end',
  },
  caloriesText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontWeight: '500',
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.surface,
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    maxHeight: '85%',
    minHeight: '60%',
  },
  modalScrollView: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCloseButton: {
    padding: spacing.sm,
  },
  modalMealInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalMealImage: {
    width: 60,
    height: 45,
    borderRadius: borderRadius.small,
    marginRight: spacing.md,
  },
  modalMealDetails: {
    flex: 1,
  },
  modalMealName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  modalMealCalories: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  modalSection: {
    paddingVertical: spacing.lg,
  },
  modalSectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.md,
  },
  modalOptionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  modalDateOption: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 50,
  },
  modalMealTypeOption: {
    width: '48%',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 70,
    position: 'relative',
  },
  modalOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  modalOptionText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  modalOptionTextSelected: {
    color: colors.surface,
  },
  modalCheckmark: {
    position: 'absolute',
    top: spacing.xs,
    right: spacing.xs,
  },
  modalActions: {
    paddingTop: spacing.lg,
    gap: spacing.md,
  },
  modalCreateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    minHeight: 50,
  },
  modalCreateButtonDisabled: {
    backgroundColor: colors.textSecondary,
    opacity: 0.6,
  },
  modalCreateButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  modalAdvancedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
    marginTop: spacing.md,
  },
  modalAdvancedText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },

  // Sort Modal Styles
  sortModalContent: {
    backgroundColor: colors.surface,
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    maxHeight: '50%',
  },
  sortOptionsContainer: {
    paddingVertical: spacing.md,
  },
  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.sm,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  sortOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  sortOptionText: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    marginLeft: spacing.md,
    fontWeight: '500',
  },
  sortOptionTextSelected: {
    color: colors.surface,
  },
  familyPreferencesSection: {
    paddingHorizontal: legacySpacing.md,
    paddingVertical: legacySpacing.md,
    backgroundColor: colors.surface,
  },
  familyPreferencesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: legacySpacing.md,
  },
  manageFamilyButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: legacySpacing.md,
    paddingVertical: legacySpacing.sm,
    borderRadius: borderRadius.medium,
  },
  manageFamilyButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    fontWeight: '600',
  },
  familyMembersContainer: {
    paddingRight: legacySpacing.md,
  },
  familyMemberCard: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: legacySpacing.md,
    marginRight: legacySpacing.md,
    minWidth: 200,
    borderWidth: 1,
    borderColor: colors.border,
  },
  familyMemberInfo: {
    flex: 1,
  },
  familyMemberName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: legacySpacing.xs,
  },
  familyMemberPreferences: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    lineHeight: 18,
  },
  preferencesBreakdown: {
    flexDirection: 'column',
    gap: 2,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  preferenceLabel: {
    fontSize: fonts.sizes.small,
    fontWeight: '600',
    color: colors.text,
  },
  preferenceValues: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    flex: 1,
  },
  noPreferences: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  familyMemberHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: legacySpacing.xs,
  },
  familyMemberAge: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontWeight: '500',
  },

  // Applied Filters Styles
  appliedFiltersContainer: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  appliedFiltersTitle: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    fontWeight: '500',
    marginBottom: spacing.sm,
  },
  filtersRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  filterTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    gap: spacing.xs,
  },
  allergyTag: {
    backgroundColor: '#FFF3E0',
  },
  filterTagText: {
    fontSize: fonts.sizes.small,
    color: '#2E7D32',
    fontWeight: '500',
  },
  allergyTagText: {
    color: '#E65100',
  },

  // Meal Tags Styles
  mealTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: legacySpacing.sm,
    marginBottom: legacySpacing.sm,
    gap: legacySpacing.xs,
  },
  mealTag: {
    paddingHorizontal: legacySpacing.sm,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    borderWidth: 1,
  },
  mealTypeTag: {
    backgroundColor: colors.primary + '15',
    borderColor: colors.primary,
  },
  dietaryTag: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
  },
  generalTag: {
    backgroundColor: colors.background,
    borderColor: colors.border,
  },
  mealTagText: {
    fontSize: fonts.sizes.tiny || 10,
    fontWeight: '500',
    color: colors.text,
    textTransform: 'capitalize',
  },
  // Performance optimization styles
  mealsContainer: {
    flex: 1,
  },
  mealRow: {
    justifyContent: 'space-between',
    paddingHorizontal: spacing.sm,
  },
  loadingMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
    gap: spacing.sm,
  },
  loadingMoreText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
  },
  loadMoreButton: {
    backgroundColor: colors.primary,
    marginHorizontal: spacing.lg,
    marginVertical: spacing.md,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
  },
  loadMoreButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
  },
});

export default HomeScreen;
