<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Meal Plan Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Meal Plan Fixes Test</h1>
    
    <div class="test-section info">
        <h3>📋 Issues Fixed:</h3>
        <ul>
            <li>✅ Added missing <code>apiService.saveMealPlan</code> function</li>
            <li>✅ Fixed History page blank screen issue</li>
            <li>✅ Updated API calls to use apiService instead of direct axios</li>
            <li>✅ Improved error handling</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🧪 Test 1: Check apiService.saveMealPlan Function</h3>
        <button onclick="testApiServiceFunction()">Test Function Exists</button>
        <div id="apiServiceResult" class="log"></div>
    </div>

    <div class="test-section">
        <h3>🧪 Test 2: Test API Connection</h3>
        <button onclick="testApiConnection()">Test Backend Connection</button>
        <div id="connectionResult" class="log"></div>
    </div>

    <div class="test-section">
        <h3>🧪 Test 3: Test Save Meal Plan (Mock Data)</h3>
        <button onclick="testSaveMealPlan()">Test Save Function</button>
        <div id="saveResult" class="log"></div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            
            // Change background color based on type
            if (type === 'success') {
                element.style.backgroundColor = '#d4edda';
            } else if (type === 'error') {
                element.style.backgroundColor = '#f8d7da';
            } else {
                element.style.backgroundColor = '#f8f9fa';
            }
        }

        async function testApiServiceFunction() {
            log('apiServiceResult', 'Testing if apiService.saveMealPlan function exists...');
            
            try {
                // Try to import the apiService (this will work if running from the website)
                const response = await fetch('/src/services/apiService.js');
                const text = await response.text();
                
                if (text.includes('saveMealPlan')) {
                    log('apiServiceResult', '✅ SUCCESS: saveMealPlan function found in apiService!', 'success');
                    
                    if (text.includes('exports.saveMealPlan') || text.includes('saveMealPlan,')) {
                        log('apiServiceResult', '✅ SUCCESS: saveMealPlan is properly exported!', 'success');
                    } else {
                        log('apiServiceResult', '⚠️ WARNING: saveMealPlan found but may not be exported', 'error');
                    }
                } else {
                    log('apiServiceResult', '❌ ERROR: saveMealPlan function not found!', 'error');
                }
            } catch (error) {
                log('apiServiceResult', `❌ ERROR: Could not check apiService: ${error.message}`, 'error');
                log('apiServiceResult', 'This test needs to be run from the website context', 'info');
            }
        }

        async function testApiConnection() {
            log('connectionResult', 'Testing backend connection...');
            
            const API_BASE_URL = 'http://localhost:5000/api';
            
            try {
                const response = await fetch(`${API_BASE_URL}/meal-plans`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token') || 'test-token'}`
                    }
                });
                
                if (response.ok) {
                    log('connectionResult', '✅ SUCCESS: Backend is responding!', 'success');
                    log('connectionResult', `Status: ${response.status} ${response.statusText}`, 'success');
                } else {
                    log('connectionResult', `⚠️ Backend responded with status: ${response.status}`, 'error');
                    const text = await response.text();
                    log('connectionResult', `Response: ${text}`, 'info');
                }
            } catch (error) {
                log('connectionResult', `❌ ERROR: Cannot connect to backend: ${error.message}`, 'error');
                log('connectionResult', 'Make sure the backend server is running on localhost:5000', 'info');
            }
        }

        async function testSaveMealPlan() {
            log('saveResult', 'Testing save meal plan with mock data...');
            
            const API_BASE_URL = 'http://localhost:5000/api';
            const mockMealPlanData = {
                name: 'Test Meal Plan',
                startDate: '2025-08-28',
                endDate: '2025-08-29',
                dietaryPreference: 'all',
                meals: {
                    '2025-08-28': {
                        breakfast: [{ name: 'Test Breakfast', id: 'test1' }],
                        lunch: [{ name: 'Test Lunch', id: 'test2' }],
                        dinner: [{ name: 'Test Dinner', id: 'test3' }]
                    }
                },
                mealTimes: {
                    breakfast: '08:00 AM',
                    lunch: '12:00 PM',
                    dinner: '06:00 PM'
                }
            };
            
            try {
                log('saveResult', 'Sending save request...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/meal-plans/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token') || 'test-token'}`
                    },
                    body: JSON.stringify(mockMealPlanData)
                });
                
                const responseData = await response.json();
                
                if (response.ok) {
                    log('saveResult', '✅ SUCCESS: Save meal plan endpoint is working!', 'success');
                    log('saveResult', `Response: ${JSON.stringify(responseData, null, 2)}`, 'success');
                } else {
                    log('saveResult', `⚠️ Save endpoint responded with status: ${response.status}`, 'error');
                    log('saveResult', `Response: ${JSON.stringify(responseData, null, 2)}`, 'error');
                }
            } catch (error) {
                log('saveResult', `❌ ERROR: Save test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            log('apiServiceResult', 'Ready to test apiService function...', 'info');
            log('connectionResult', 'Ready to test backend connection...', 'info');
            log('saveResult', 'Ready to test save meal plan...', 'info');
        };
    </script>
</body>
</html>
