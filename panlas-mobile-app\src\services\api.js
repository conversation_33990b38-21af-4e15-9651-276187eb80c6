import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  mockMeals,
  mockFamilyMembers,
  mockRecentlyViewed,
  mockRecentlyAddedToMealPlans,
  mockMealPlans,
  mockSavedMealPlans
} from './mockData';

// Base URL for your backend - Production deployment
const getLocalIP = () => {
  // Return production domain for deployment
  return 'www.panlasapp.food';
};

const BASE_URL = `https://${getLocalIP()}/api`;
const IMAGE_BASE_URL = `https://${getLocalIP()}`;

// Export BASE_URL for use in other components
export { BASE_URL, IMAGE_BASE_URL };

// Helper function to fix image URLs
const fixImageUrl = (imageUrl) => {
  if (!imageUrl) return null;
  if (imageUrl.startsWith('http')) return imageUrl; // Already a full URL
  if (imageUrl.startsWith('/imagesfood/')) {
    return `${IMAGE_BASE_URL}${imageUrl}`;
  }
  return imageUrl;
};

// Helper function to process meal data and fix image URLs
const processMealData = (meals) => {
  if (!Array.isArray(meals)) return meals;
  return meals.map(meal => ({
    ...meal,
    image: fixImageUrl(meal.image)
  }));
};

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle network errors gracefully
    if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
      console.warn('Network error - backend may be unavailable:', error.message);
      // Return a mock response for development
      return Promise.resolve({
        data: [],
        status: 200,
        statusText: 'OK (Mock Response)'
      });
    }

    if (error.response?.status === 401) {
      // Token expired or invalid
      await AsyncStorage.removeItem('token');
      await AsyncStorage.removeItem('user');
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  login: (credentials) => api.post('/users/login', credentials).catch((error) => {
    console.error('Login API error:', error);
    // Re-throw the error instead of returning empty data
    throw error;
  }),
  logout: () => api.post('/users/logout').catch((error) => {
    console.error('Logout API error:', error);
    // Don't throw error for logout, just log it
    return { data: {} };
  }),
  register: (userData) => api.post('/users/signup', userData).catch((error) => {
    console.error('Registration API error:', error);
    // Re-throw the error so it can be properly handled in AuthContext
    throw error;
  }),
  getProfile: () => api.get('/users/profile').catch(() => ({ data: {} })),
  verifyEmail: (token) => api.get(`/users/verify-email?token=${token}`).catch(() => ({ data: {} })),
  resendVerification: (email) => api.post('/users/resend-verification', { email }).catch(() => ({ data: {} })),
  requestPasswordReset: (email) => api.post('/password-reset/forgot', { email }).catch(() => ({ data: {} })),
  verifyResetToken: (token) => api.post('/password-reset/verify-token', { token }).catch(() => ({ data: {} })),
  resetPassword: (token, newPassword) => api.post('/password-reset/reset', { token, newPassword }).catch(() => ({ data: {} })),
  // OTP methods
  verifyOTP: (email, otp) => api.post('/users/verify-otp', { email, otp }).catch(() => ({ data: {} })),
  resendOTP: (email) => api.post('/users/resend-otp', { email }).catch(() => ({ data: {} })),
  updateProfile: (data) => api.put('/users/profile', data).catch(() => ({ data: {} })),
  changePassword: (data) => api.put('/users/change-password', data).catch(() => ({ data: {} })),
};

// Meals API calls
export const mealsAPI = {
  getAllMeals: () => api.get('/meals')
    .then(response => {
      const meals = response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals })),
  getFilipinoDishes: (params) => api.get('/meals/filipino', { params })
    .then(response => {
      const meals = response.data.meals || response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals })),
  getMealById: async (id) => {
    try {
      // First try the Filipino dishes endpoint
      console.log('Trying Filipino dishes endpoint for ID:', id);
      const response = await api.get(`/meals/filipino/${id}`);
      const meal = response.data.meal || response.data || null;
      if (meal) {
        console.log('Successfully fetched meal from Filipino endpoint');
        return { data: { ...meal, image: fixImageUrl(meal.image) } };
      }
    } catch (error) {
      console.log('Filipino endpoint failed, trying general meals endpoint:', error.message);

      try {
        // Fallback to general meals endpoint
        const response = await api.get(`/meals/${id}`);
        const meal = response.data.meal || response.data || null;
        if (meal) {
          console.log('Successfully fetched meal from general endpoint');
          return { data: { ...meal, image: fixImageUrl(meal.image) } };
        }
      } catch (fallbackError) {
        console.log('Both endpoints failed, using mock data:', fallbackError.message);
      }
    }

    // Final fallback to mock data
    const mockMeal = mockMeals.find(meal => meal.id === parseInt(id)) || null;
    console.log('Using mock meal data:', mockMeal ? 'found' : 'not found');
    return { data: mockMeal };
  },
  searchMeals: (params) => api.get('/meals/search/filters', { params })
    .then(response => {
      const meals = response.data.meals || response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals })),
  getMealSuggestions: (params) => api.get('/meals/suggestions/type', { params })
    .then(response => {
      const meals = response.data.meals || response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals.slice(0, 4) })),
  getPopularMeals: () => api.get('/meals/popular/list')
    .then(response => {
      const meals = response.data.popularMeals || response.data || [];
      return { data: processMealData(meals) };
    })
    .catch(() => ({ data: mockMeals.slice(0, 6) })),
  getMealRecommendations: (params) => api.get('/meals/recommendations', { params })
    .then(response => {
      const recommendations = response.data.recommendations || response.data || [];
      return {
        data: processMealData(recommendations),
        appliedFilters: response.data.appliedFilters || {}
      };
    })
    .catch(error => {
      console.log('Meal recommendations API failed, using fallback:', error.message);
      return { data: mockMeals.slice(0, 10), appliedFilters: {} };
    }),
  createMeal: (mealData) => api.post('/meals', mealData).catch(() => ({ data: {} })),
};

// Meal Plans API calls
export const mealPlansAPI = {
  // Get all meal plans for authenticated user with optional date range
  getMealPlans: (params = {}) => {
    const { startDate, endDate } = params;
    const queryParams = {};
    if (startDate) queryParams.startDate = startDate;
    if (endDate) queryParams.endDate = endDate;

    return api.get('/meal-plans', { params: queryParams })
      .then(response => {
        console.log('Meal plans API response:', response.data);
        return { data: response.data || [] };
      })
      .catch(error => {
        console.log('Meal plans API failed, using mock data:', error.message);
        return { data: mockMealPlans || [] };
      });
  },

  // Get specific meal plan by date
  getMealPlanByDate: (date) => {
    return api.get(`/meal-plans/${date}`)
      .then(response => ({ data: response.data }))
      .catch(() => ({
        data: mockMealPlans?.find(plan => plan.date === date) || null
      }));
  },

  // Create or update meal plan (date, mealType, meal)
  createOrUpdateMealPlan: (data) => {
    return api.post('/meal-plans', data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Create/update meal plan failed:', error);
        return { data: {} };
      });
  },

  // Save complete meal plan as template
  saveMealPlan: (data) => {
    return api.post('/meal-plans/save', data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Save meal plan failed:', error);
        return { data: {} };
      });
  },

  // Validate meal plan before saving
  validateMealPlan: (selectedMeals) => {
    return api.post('/meal-plans/validate', { selectedMeals })
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Validate meal plan failed:', error);
        return {
          data: {
            success: false,
            message: 'Validation failed',
            validation: {
              hasIssues: false,
              memberValidations: [{ memberName: 'You', concern: 'Validation temporarily unavailable' }],
              totalCalories: 0
            }
          }
        };
      });
  },

  // Get saved meal plan templates
  getSavedMealPlans: () => {
    return api.get('/meal-plans/saved')
      .then(response => ({ data: response.data || [] }))
      .catch(() => ({ data: mockSavedMealPlans || [] }));
  },

  // Favorite meal plan templates
  getFavoriteMealPlans: () => {
    return api.get('/users/favorite-meal-plans')
      .then(response => ({ data: response.data || [] }))
      .catch(() => ({ data: [] }));
  },

  addFavoriteMealPlan: (mealPlanData) => {
    return api.post('/users/favorite-meal-plans', mealPlanData)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Add favorite meal plan failed:', error);
        return { data: {} };
      });
  },

  removeFavoriteMealPlan: (templateId) => {
    return api.delete(`/users/favorite-meal-plans/${templateId}`)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Remove favorite meal plan failed:', error);
        return { data: {} };
      });
  },

  // Get user's meal plans with filtering
  getUserMealPlans: (params = {}) => {
    return api.get('/meal-plans/user/plans', { params })
      .then(response => ({ data: response.data?.mealPlans || [] }))
      .catch(() => ({ data: mockMealPlans || [] }));
  },

  // Toggle meal plan lock status
  toggleLockMealPlan: (date, data) => {
    return api.put(`/meal-plans/${date}/lock`, data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Toggle lock failed:', error);
        return { data: {} };
      });
  },

  // Mark meal as completed/incomplete
  markMealCompleted: (date, data) => {
    return api.put(`/meal-plans/${date}/complete`, data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Mark meal completed failed:', error);
        return { data: {} };
      });
  },

  // Remove meal from plan
  removeMealFromPlan: (date, data) => {
    return api.delete(`/meal-plans/${date}/meals`, { data })
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Remove meal failed:', error);
        return { data: {} };
      });
  },

  // Delete entire meal plan for a date
  deleteMealPlan: (date) => {
    console.log('🔥 API: deleteMealPlan called with date:', date);
    console.log('🔥 API: Making DELETE request to:', `/meal-plans/${date}`);

    return api.delete(`/meal-plans/${date}`)
      .then(response => {
        console.log('🔥 API: Delete successful, response:', response.data);
        return { data: response.data };
      })
      .catch(error => {
        console.error('🔥 API: Delete meal plan failed:', error);
        console.error('🔥 API: Error response:', error.response?.data);
        console.error('🔥 API: Error status:', error.response?.status);
        throw error; // Re-throw the error so the calling code can handle it
      });
  },

  // Update meal times
  updateMealTimes: (date, data) => {
    return api.put(`/meal-plans/${date}/meal-times`, data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Update meal times failed:', error);
        return { data: {} };
      });
  },

  // Create meal plan from template
  createFromTemplate: (data) => {
    return api.post('/meal-plans/from-template', data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Create from template failed:', error);
        return { data: {} };
      });
  },

  // Create template from meal plan
  createTemplateFromMealPlan: (data) => {
    return api.post('/meal-plans/create-template', data)
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Create template failed:', error);
        return { data: {} };
      });
  },

  // Get meal recommendations
  getMealRecommendations: (params = {}) => {
    return api.get('/meal-plans/recommendations', { params })
      .then(response => ({ data: response.data || [] }))
      .catch(() => ({ data: mockMeals?.slice(0, 5) || [] }));
  },

  // Generate meal plan automatically
  generateMealPlan: (data) => {
    console.log('🚀 Making API call to generate meal plan');
    console.log('📤 Request URL:', `${BASE_URL}/meal-plans/generate`);
    console.log('📤 Request data:', data);

    return api.post('/meal-plans/generate', data)
      .then(response => {
        console.log('✅ Generate meal plan API success');
        console.log('📥 Response status:', response.status);
        console.log('📥 Response data:', response.data);

        // Check if the response is successful based on HTTP status
        if (response.status === 200 || response.status === 201) {
          // If backend returns an array instead of proper object, wrap it
          if (Array.isArray(response.data)) {
            console.log('⚠️ Backend returned array instead of object, wrapping it');
            return {
              data: {
                success: true,
                message: 'Meal plans generated successfully',
                generatedPlans: response.data.length,
                plans: response.data
              }
            };
          }
          return { data: response.data };
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      })
      .catch(error => {
        console.error('❌ Generate meal plan API failed:', error);
        console.error('📥 Error response data:', error.response?.data);
        console.error('📥 Error status:', error.response?.status);
        console.error('📥 Error message:', error.message);
        console.error('📥 Full error:', error);
        // Re-throw the error so it can be handled properly in the UI
        throw error;
      });
  },

  // Update meal plans for dietary changes
  updateMealPlansForDietaryChanges: (dietaryPreferences) => {
    return api.post('/meal-plans/update-for-dietary-changes', { dietaryPreferences })
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Update meal plans for dietary changes failed:', error);
        return { data: { success: false, error: error.message } };
      });
  },

  // Check for dietary conflicts
  checkDietaryConflicts: (dietaryPreferences = null) => {
    const params = dietaryPreferences ? { dietaryPreferences: JSON.stringify(dietaryPreferences) } : {};
    return api.get('/meal-plans/dietary-conflicts', { params })
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Check dietary conflicts failed:', error);
        return { data: { success: false, conflicts: [], error: error.message } };
      });
  },

  // Resolve dietary conflicts
  resolveDietaryConflicts: (conflicts, dietaryPreferences) => {
    return api.post('/meal-plans/resolve-conflicts', { conflicts, dietaryPreferences })
      .then(response => ({ data: response.data }))
      .catch(error => {
        console.error('Resolve dietary conflicts failed:', error);
        return { data: { success: false, error: error.message } };
      });
  },
};

// User Features API calls
export const userAPI = {
  updateProfile: (data) => api.put('/users/profile', data).catch(() => ({ data: {} })),
  getFavoriteMeals: () => api.get('/users/favorite-meals').catch(() => ({ data: mockMeals.slice(0, 3) })),
  addFavoriteMeal: (mealData) => api.post('/users/favorite-meals', mealData).catch(() => ({ data: {} })),
  removeFavoriteMeal: (mealId) => api.delete(`/users/favorite-meals/${mealId}`).catch(() => ({ data: {} })),
  getFavoriteMealPlans: () => api.get('/users/favorite-meal-plans').catch(() => ({ data: [] })),
  addFavoriteMealPlan: (mealPlanData) => api.post('/users/favorite-meal-plans', mealPlanData).catch(() => ({ data: {} })),
  removeFavoriteMealPlan: (planId) => api.delete(`/users/favorite-meal-plans/${planId}`).catch(() => ({ data: {} })),
  getRecentlyViewedMeals: () => api.get('/users/recently-viewed-meals').catch((error) => {
    console.error('Error fetching recently viewed meals:', error);
    return { data: { recentlyViewedMeals: [] } };
  }),
  addRecentlyViewedMeal: (mealData) => api.post('/users/recently-viewed-meals', mealData).catch((error) => {
    console.error('Error adding recently viewed meal:', error);
    return { data: {} };
  }),
  clearRecentlyViewedMeals: () => {
    console.log('🗑️ API: Making DELETE request to /users/recently-viewed-meals');
    return api.delete('/users/recently-viewed-meals')
      .then(response => {
        console.log('🗑️ API: Clear recently viewed meals success:', response.data);
        return { data: response.data };
      })
      .catch((error) => {
        console.error('🗑️ API: Error clearing recently viewed meals:', error);
        console.error('🗑️ API: Error response:', error.response?.data);
        console.error('🗑️ API: Error status:', error.response?.status);
        throw error; // Re-throw to let the calling function handle it
      });
  },
  getRecentlyAddedToMealPlans: () => api.get('/users/recently-added-to-meal-plans').catch(() => ({ data: mockRecentlyAddedToMealPlans })),
  addRecentlyAddedToMealPlan: (mealData) => api.post('/users/recently-added-to-meal-plans', mealData).catch(() => ({ data: {} })),
  clearRecentlyAddedToMealPlans: () => api.delete('/users/recently-added-to-meal-plans').catch((error) => {
    console.error('Error clearing recently added to meal plans:', error);
    return { data: {} };
  }),
  getDietaryPreferences: () => {
    return api.get('/users/dietary-preferences')
      .then(response => {
        console.log('Dietary preferences API response:', response.data);
        return { data: response.data || {} };
      })
      .catch(error => {
        console.log('Dietary preferences API failed, using mock data:', error.message);
        return { data: { restrictions: ['Vegetarian'], allergies: ['Nuts'], calorieTarget: '2000' } };
      });
  },
  updateDietaryPreferences: (data) => api.put('/users/dietary-preferences', data).catch(() => ({ data: {} })),
  getFamilyMembers: () => {
    return api.get('/users/family-members')
      .then(response => {
        console.log('Family members API response:', response.data);
        // Backend returns { familyMembers: [...] }
        return { data: response.data.familyMembers || [] };
      })
      .catch(error => {
        console.log('Family members API failed, using mock data:', error.message);
        return { data: mockFamilyMembers || [] };
      });
  },
  addFamilyMember: (memberData) => {
    return api.post('/users/family-members', memberData)
      .then(response => {
        console.log('Add family member response:', response.data);
        // Backend returns { success: true, familyMembers: [...] }
        return { data: response.data.familyMembers || [] };
      })
      .catch(error => {
        console.error('Add family member failed:', error);
        return { data: {} };
      });
  },
  removeFamilyMember: (memberId) => {
    return api.delete(`/users/family-members/${memberId}`)
      .then(response => {
        console.log('Remove family member response:', response.data);
        // Backend returns { success: true, familyMembers: [...] }
        return { data: response.data.familyMembers || [] };
      })
      .catch(error => {
        console.error('Remove family member failed:', error);
        return { data: {} };
      });
  },
  updateFamilyMember: (memberId, memberData) => {
    return api.put(`/users/family-members/${memberId}`, memberData)
      .then(response => {
        console.log('Update family member response:', response.data);
        // Backend returns { success: true, familyMembers: [...] }
        return { data: response.data.familyMembers || [] };
      })
      .catch(error => {
        console.error('Update family member failed:', error);
        return { data: {} };
      });
  },
};

// Feedback API calls
export const feedbackAPI = {
  submitFeedback: (data) => api.post('/feedback/submit', data),
};

// Activity API calls
export const activityAPI = {
  logActivity: (data) => api.post('/activity/log', data),
};

// AI API calls
export const aiAPI = {
  detectDietaryConflicts: (preferences) => api.post('/ai/dietary-conflicts', preferences),
  getMealRecommendations: (data) => api.post('/ai/meal-recommendations', data),
  getGoalSuggestions: (data) => api.post('/ai/goal-suggestions', data),
  chat: (data) => api.post('/ai/chat', data),
  getGoals: () => api.get('/ai/goals'),
  generateMealPlan: (data = {}) => api.post('/ai/generate-meal-plan', data),
  generateWeeklyMealPlan: (data = {}) => api.post('/ai/generate-weekly-meal-plan', data),
  editMealPlan: (data) => api.post('/ai/edit-meal-plan', data),
  // AI meal analysis for family compatibility
  analyzeMealsForFamily: (data) => {
    return api.post('/ai/analyze-meals', data)
      .then(response => ({
        success: true,
        analysis: response.data
      }))
      .catch(error => {
        console.error('AI meal analysis failed:', error);
        return {
          success: false,
          error: error.message,
          analysis: null
        };
      });
  },
  setCalorieGoal: (calorieGoal) => api.post('/ai/set-calorie-goal', { calorieGoal }),
};

export default api;
