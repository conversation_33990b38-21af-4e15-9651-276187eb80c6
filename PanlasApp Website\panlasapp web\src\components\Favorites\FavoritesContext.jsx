// $JAVASCRIPT:src/components/Favorites/FavoritesContext.js
import React, { createContext, useContext, useState, useEffect } from "react";
import axios from "axios";

const FavoritesContext = createContext();

export { FavoritesContext };
export const useFavorites = () => useContext(FavoritesContext);

export const FavoritesProvider = ({ children }) => {
  const [favorites, setFavorites] = useState([]);
  const [favoriteMealPlans, setFavoriteMealPlans] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadFavorites = async () => {
    const token = localStorage.getItem("token");

    if (!token) {
      setFavorites([]);
      setFavoriteMealPlans([]);
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      // Load favorite meals
      const mealsRes = await axios.get(`${API_BASE_URL}/users/favorite-meals`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setFavorites(mealsRes.data.favoriteMeals || []);

      // Load favorite meal plans
      const plansRes = await axios.get(`${API_BASE_URL}/users/favorite-meal-plans`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setFavoriteMealPlans(plansRes.data.favoriteMealPlans || []);
    } catch (error) {
      console.error('Error loading favorites:', error);
      setFavorites([]);
      setFavoriteMealPlans([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFavorites();
  }, []);

  const addFavorite = async (meal) => {
    const token = localStorage.getItem("token");
    if (!token) {
      console.log('No token found, cannot add favorite');
      return { success: false, error: 'No authentication token' };
    }

    console.log('Adding favorite meal:', meal);

    try {
      // Transform meal data to match backend schema expectations
      const transformedMeal = {
        ...meal,
        // Convert category array to string (take first element or empty string)
        category: Array.isArray(meal.category) ? (meal.category[0] || '') : (meal.category || ''),
        // Ensure other array fields are properly formatted
        mealType: Array.isArray(meal.mealType) ? meal.mealType : [meal.mealType].filter(Boolean),
        dietaryTags: Array.isArray(meal.dietaryTags) ? meal.dietaryTags : [meal.dietaryTags].filter(Boolean),
        ingredients: Array.isArray(meal.ingredients) ? meal.ingredients : [meal.ingredients].filter(Boolean),
        instructions: Array.isArray(meal.instructions) ? meal.instructions : [meal.instructions].filter(Boolean),
        // Ensure rating is an array of strings
        rating: Array.isArray(meal.rating) ? meal.rating.map(String) : [String(meal.rating || '')].filter(Boolean)
      };

      console.log('Transformed meal for backend:', transformedMeal);

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const res = await axios.post(
        `${API_BASE_URL}/users/favorite-meals`,
        { meal: transformedMeal },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log('Add favorite response:', res.data);

      // Update local state - either use server response or add locally
      if (res.data && res.data.favoriteMeals) {
        setFavorites(res.data.favoriteMeals);
      } else {
        setFavorites(prev => {
          const currentFavorites = Array.isArray(prev) ? prev : [];
          return [...currentFavorites, meal];
        });
      }

      console.log('Successfully added favorite');
      return { success: true };
    } catch (error) {
      console.error('Error adding favorite meal:', error);
      console.error('Error response:', error.response?.data);
      return { success: false, error: error.response?.data?.message || 'Failed to add favorite' };
    }
  };

  const removeFavorite = async (mealId) => {
    const token = localStorage.getItem("token");
    if (!token) {
      console.log('No token found, cannot remove favorite');
      return { success: false, error: 'No authentication token' };
    }

    console.log('Removing favorite meal ID:', mealId);

    try {
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const res = await axios.delete(
        `${API_BASE_URL}/users/favorite-meals/${mealId}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log('Remove favorite response:', res.data);

      // Update local state - either use server response or remove locally
      if (res.data && res.data.favoriteMeals) {
        setFavorites(res.data.favoriteMeals);
      } else {
        setFavorites(prev => {
          const currentFavorites = Array.isArray(prev) ? prev : [];
          return currentFavorites.filter(meal => (meal.id || meal._id) !== mealId);
        });
      }

      console.log('Successfully removed favorite');
      return { success: true };
    } catch (error) {
      console.error('Error removing favorite meal:', error);
      console.error('Error response:', error.response?.data);
      return { success: false, error: error.response?.data?.message || 'Failed to remove favorite' };
    }
  };

  const isFavorite = (mealId) => {
    const safeFavorites = Array.isArray(favorites) ? favorites : [];
    return safeFavorites.some((fav) => (fav._id || fav.id) === mealId);
  };

  // Meal Plan Favorites Functions
  const addFavoriteMealPlan = async (mealPlanData) => {
    const token = localStorage.getItem("token");
    if (!token) return;
    try {
      console.log('🔍 FavoritesContext: Sending meal plan data to backend:', JSON.stringify(mealPlanData, null, 2));

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const res = await axios.post(
        `${API_BASE_URL}/users/favorite-meal-plans`,
        mealPlanData,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log('✅ FavoritesContext: Backend response:', res.data);
      setFavoriteMealPlans(res.data.favoriteMealPlans || []);
      return { success: true };
    } catch (err) {
      console.error('❌ FavoritesContext: Error adding favorite meal plan:', err);
      console.error('❌ FavoritesContext: Error response:', err.response?.data);
      return { success: false, error: err.response?.data?.message || 'Failed to add favorite meal plan' };
    }
  };

  const removeFavoriteMealPlan = async (planId) => {
    const token = localStorage.getItem("token");
    if (!token) return;
    try {
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const res = await axios.delete(
        `${API_BASE_URL}/users/favorite-meal-plans/${planId}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setFavoriteMealPlans(res.data.favoriteMealPlans || []);
      return { success: true };
    } catch (err) {
      console.error('Error removing favorite meal plan:', err);
      return { success: false, error: err.response?.data?.message || 'Failed to remove favorite meal plan' };
    }
  };

  const isFavoriteMealPlan = (planId) => {
    return favoriteMealPlans.some((plan) =>
      (plan.plan?._id || plan.plan?.id || plan._id || plan.id) === planId
    );
  };

  return (
    <FavoritesContext.Provider
      value={{
        favorites,
        favoriteMealPlans,
        loading,
        loadFavorites,
        addFavorite,
        removeFavorite,
        isFavorite,
        addFavoriteMealPlan,
        removeFavoriteMealPlan,
        isFavoriteMealPlan,
      }}
    >
      {children}
    </FavoritesContext.Provider>
  );
};
