// Test script to verify admin activity filtering
const mongoose = require('mongoose');
const User = require('./models/User');

async function testAdminFilter() {
  try {
    console.log('🔍 Testing admin filter logic...');

    // Simulate the admin filtering logic
    const adminUsers = [
      { _id: 'admin1', username: 'admin', email: '<EMAIL>', isAdmin: true },
      { _id: 'admin2', username: 'superadmin', email: '<EMAIL>', isAdmin: true }
    ];

    const regularUsers = [
      { _id: 'user1', username: 'anaskhan', email: '<EMAIL>', isAdmin: false },
      { _id: 'user2', username: 'john', email: '<EMAIL>', isAdmin: false }
    ];

    const allUsers = [...adminUsers, ...regularUsers];
    const adminUserIds = adminUsers.map(user => user._id);

    console.log('👥 All users:', allUsers.map(u => `${u.username} (Admin: ${u.isAdmin})`));
    console.log('👑 Admin user IDs:', adminUserIds);

    // Simulate activities
    const activities = [
      { user: 'admin1', action: 'disabled_account', createdAt: new Date() },
      { user: 'user1', action: 'login', createdAt: new Date() },
      { user: 'admin2', action: 'made_admin', createdAt: new Date() },
      { user: 'user2', action: 'submitted_feedback', createdAt: new Date() },
      { user: 'admin1', action: 'deleted_feedback', createdAt: new Date() }
    ];

    console.log('\n📋 All activities:');
    activities.forEach((activity, index) => {
      const user = allUsers.find(u => u._id === activity.user);
      console.log(`${index + 1}. ${activity.action} by ${user.username} (Admin: ${user.isAdmin})`);
    });

    // Filter to only admin activities
    const adminActivities = activities.filter(activity => 
      adminUserIds.includes(activity.user)
    );

    console.log('\n👑 Admin activities only:');
    adminActivities.forEach((activity, index) => {
      const user = allUsers.find(u => u._id === activity.user);
      console.log(`${index + 1}. ${activity.action} by ${user.username} (Admin: ${user.isAdmin})`);
    });

    console.log('\n✅ Admin filtering test completed!');
    console.log(`📊 Total activities: ${activities.length}`);
    console.log(`👑 Admin activities: ${adminActivities.length}`);
    console.log(`👤 Regular user activities filtered out: ${activities.length - adminActivities.length}`);

  } catch (error) {
    console.error('❌ Error in admin filter test:', error);
  }
}

testAdminFilter();
