const axios = require('axios');

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY || 'AIzaSyApLfuTDjZfegcpfzKqL37OpmL3WB3cALA';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    this.model = 'gemini-2.0-flash';

    // Available dietary restrictions in the app (must match frontend options exactly)
    this.AVAILABLE_DIETARY_RESTRICTIONS = [
      'Vegetarian',
      'Vegan',
      'Dairy-Free',
      'Egg-Free',
      'Gluten-Free',
      'Soy-Free',
      'Nut-Free',
      'Low-Carb',
      'Low-Sugar',
      'Sugar-Free',
      'Low-Fat',
      'Low-Sodium',
      'Organic',
      'Halal',
      'High-Protein',
      'Pescatarian',
      'Keto',
      'Plant-Based',
      'Kosher',
      'Climatarian',
      'Raw Food',
      'Mediterranean',
      'Paleo',
      'Kangatarian',
      'Pollotarian',
      'Flexitarian'
    ];

    // Available allergy options in the app (must match frontend options exactly)
    this.AVAILABLE_ALLERGIES = [
      'Milk',
      'Eggs',
      'Fish',
      'Shellfish',
      'Peanuts',
      'Tree Nuts',
      'Wheat',
      'Soybeans',
      'Sesame',
      'Mustard',
      'Celery',
      'Lupin',
      'Mollusks',
      'Sulfites'
    ];
  }

  async generateContent(prompt) {
    try {
      const response = await axios.post(
        `${this.baseUrl}?key=${this.apiKey}`,
        {
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 4096,
          }
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('Gemini API Error:', error.response?.data || error.message);
      throw new Error('Failed to generate AI response');
    }
  }

  // Detect conflicts in dietary preferences
  async detectDietaryConflicts(data) {
    // Handle both old format (direct preferences) and new format (with user and family)
    let userPreferences, familyMembers;
    if (data.userPreferences) {
      // New format with user and family preferences
      userPreferences = data.userPreferences;
      familyMembers = data.familyMembers || [];
    } else {
      // Old format - direct preferences (for backward compatibility)
      userPreferences = {
        restrictions: data.restrictions || [],
        allergies: data.allergies || [],
        dislikedIngredients: data.dislikedIngredients || []
      };
      familyMembers = [];
    }

    // Dietary definitions for Gemini (improves reasoning for new/rare options)
    const definitions = `
DEFINITIONS:
- Vegetarian: No meat, poultry, or fish.
- Vegan: No animal products at all.
- Dairy-Free: No milk or milk products.
- Egg-Free: No eggs or egg products.
- Gluten-Free: No wheat, barley, rye, or gluten-containing grains.
- Soy-Free: No soy or soy products.
- Nut-Free: No peanuts or tree nuts.
- Low-Carb: Limited carbohydrate intake.
- Low-Sugar: Limited sugar intake.
- Sugar-Free: No sugar.
- Low-Fat: Limited fat intake.
- Low-Sodium: Limited salt intake.
- Organic: Only organic foods.
- Halal: Permitted by Islamic law.
- High-Protein: High in protein.
- Pescatarian: No meat except fish/seafood.
- Keto: Very low carb, high fat.
- Plant-Based: Mostly or only plants.
- Kosher: Permitted by Jewish law.
- Climatarian: Diet chosen for low environmental impact.
- Raw Food: Only raw, uncooked foods.
- Mediterranean: Based on Mediterranean region foods.
- Paleo: Foods presumed eaten by early humans.
- Kangatarian: Vegetarian but eats kangaroo meat.
- Pollotarian: Vegetarian but eats poultry.
- Flexitarian: Mostly vegetarian but occasionally eats meat or fish.

ALLERGY DEFINITIONS:
- Milk: Allergy to cow's milk and dairy products.
- Eggs: Allergy to eggs and egg products.
- Fish: Allergy to finned fish.
- Shellfish: Allergy to crustaceans and mollusks.
- Peanuts: Allergy to peanuts (legumes).
- Tree Nuts: Allergy to nuts from trees (e.g., almonds, walnuts).
- Wheat: Allergy to wheat and wheat products.
- Soybeans: Allergy to soy and soy products.
- Sesame: Allergy to sesame seeds and products.
- Mustard: Allergy to mustard seeds and products.
- Celery: Allergy to celery and celery products.
- Lupin: Allergy to lupin beans and products.
- Mollusks: Allergy to mollusks (e.g., clams, oysters).
- Sufites: Sensitivity to sulfite preservatives.
`;

    let prompt = `
${definitions}
Analyze the following dietary preferences for conflicts and contradictions:
USER PREFERENCES:
Dietary Restrictions: ${userPreferences.restrictions?.join(', ') || 'None'}
Allergies: ${userPreferences.allergies?.join(', ') || 'None'}
Disliked Ingredients: ${userPreferences.dislikedIngredients?.join(', ') || 'None'}
`;

    if (familyMembers.length > 0) {
      prompt += `
FAMILY MEMBERS PREFERENCES:
`;
      familyMembers.forEach((member) => {
        prompt += `
${member.name}:
- Dietary Restrictions: ${member.restrictions?.join(', ') || 'None'}
- Allergies: ${member.allergies?.join(', ') || 'None'}
- Disliked Ingredients: ${member.dislikedIngredients?.join(', ') || 'None'}
`;
      });
    }

    prompt += `
Please identify any contradictions or conflicts:
1. WITHIN the user's own preferences (e.g., Keto + Vegan)
2. BETWEEN the user and family members (e.g., User: Keto, Family Member: Vegan)
Consider all dietary restrictions and allergies listed above, including less common or new ones. If you are unsure about a restriction, do your best based on the definition provided.

Examples of conflicts:
- Keto + Vegan can be challenging due to limited protein sources
- Dairy-free + Lactose intolerance is redundant
- User wants Keto but family member is Vegan (meal planning conflicts)

Return your response in this JSON format:
{
  "hasConflicts": true/false,
  "conflicts": [
    {
      "items": ["preference1", "preference2"],
      "reason": "explanation of the conflict",
      "severity": "high/medium/low",
      "type": "user" or "family" or "user-family"
    }
  ],
  "suggestions": ["suggestion1", "suggestion2"]
}
Only return the JSON, no additional text.
`;

    try {
      const response = await this.generateContent(prompt);
      // Clean the response to extract JSON from markdown code blocks
      const cleanedResponse = this.extractJsonFromResponse(response);
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Error detecting dietary conflicts:', error);
      return { hasConflicts: false, conflicts: [], suggestions: [] };
    }
  }

  // Generate meal recommendations based on family profile
  async generateMealRecommendations(familyProfile, availableMeals, goalType = null) {
    // Create a comprehensive profile summary
    const profileSummary = this.createProfileSummary(familyProfile);
    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
       - Vegan: ${meal.dietType?.isVegan ? 'Yes' : 'No'}
       - Gluten-Free: ${meal.dietType?.isGlutenFree ? 'Yes' : 'No'}
       - Dairy-Free: ${meal.dietType?.isDairyFree ? 'Yes' : 'No'}
       - Nut-Free: ${meal.dietType?.isNutFree ? 'Yes' : 'No'}
       - Low-Carb: ${meal.dietType?.isLowCarb ? 'Yes' : 'No'}
       - Keto: ${meal.dietType?.isKeto ? 'Yes' : 'No'}
       - Pescatarian: ${meal.dietType?.isPescatarian ? 'Yes' : 'No'}
       - Halal: ${meal.dietType?.isHalal ? 'Yes' : 'No'}
    `).join('');
    const hostName = familyProfile.userName || 'User';

    const prompt = `
You are a nutritionist AI helping a Filipino family plan their meals. You MUST ONLY recommend meals from the exact list provided below.
FAMILY PROFILE:
${profileSummary}
${goalType ? `CURRENT GOAL: ${goalType}` : ''}
AVAILABLE MEALS IN DATABASE (you can ONLY choose from these):
${mealList}
IMPORTANT RULES:
1. You MUST ONLY recommend meals from the list above
2. Use the EXACT meal names as written in quotes
3. Recommend 5-8 meals maximum
4. Consider BOTH the host user (${hostName}) AND family members' dietary restrictions, allergies, and goals
5. Prioritize meals that match their preferences
6. If there are conflicts between family members, prioritize the host user (${hostName})
Return your response in this JSON format:
{
  "recommendations": [
    {
      "mealName": "EXACT meal name from the list",
      "reason": "why this meal is recommended for this family",
      "nutritionalBenefits": "key nutritional benefits",
      "suitability": "how it fits the family profile"
    }
  ],
  "generalAdvice": "Start with 'Hello ${hostName}!' and provide overall dietary advice for this family"
}
Only return the JSON, no additional text. Remember: ONLY use meal names that appear in the provided list.
`;
    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);
      // Validate that all recommended meals exist in the database
      const validatedRecommendations = aiResponse.recommendations.filter(rec => {
        const mealExists = availableMeals.some(meal =>
          meal.name.toLowerCase() === rec.mealName.toLowerCase()
        );
        if (!mealExists) {
          console.warn(`AI recommended non-existent meal: ${rec.mealName}`);
        }
        return mealExists;
      });
      return {
        recommendations: validatedRecommendations,
        generalAdvice: aiResponse.generalAdvice || ''
      };
    } catch (error) {
      console.error('Error generating meal recommendations:', error);
      return { recommendations: [], generalAdvice: '' };
    }
  }

  // Generate goal-based dietary suggestions
  async generateGoalBasedSuggestions(goal, healthCondition = null) {
    let prompt = `
You are a nutritionist AI. A user has selected the goal: "${goal}".
${healthCondition ? `They also have the health condition: "${healthCondition}".` : ''}
Based on this goal${healthCondition ? ' and health condition' : ''}, suggest appropriate dietary preferences from ONLY these available options in our app:
Available Dietary Restrictions: ${this.AVAILABLE_DIETARY_RESTRICTIONS.join(', ')}
Available Allergies to Avoid: ${this.AVAILABLE_ALLERGIES.join(', ')}
IMPORTANT: You can ONLY recommend dietary restrictions and allergies from the lists above. Do not suggest any options that are not in these lists.
Provide specific recommendations and explain why each suggestion is beneficial for their goal${healthCondition ? ' and condition' : ''}.
Return your response in this JSON format:
{
  "recommendedRestrictions": ["restriction1", "restriction2"],
  "recommendedAllergies": ["allergy1", "allergy2"],
  "explanation": "detailed explanation of why these are recommended",
  "additionalTips": ["tip1", "tip2", "tip3"]
}
Only return the JSON, no additional text. Remember: ONLY use options from the provided lists.
`;
    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Error generating goal-based suggestions:', error);
      return {
        recommendedRestrictions: [],
        recommendedAllergies: [],
        explanation: '',
        additionalTips: []
      };
    }
  }

  // Generate calorie-based meal recommendations
  async generateCalorieBasedRecommendations(userProfile, availableMeals, calorieGoal) {
    const profileSummary = this.createProfileSummary(userProfile);

    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
    `).join('\n');

    const prompt = `
You are a nutritionist AI helping a user achieve their daily calorie goal of ${calorieGoal} calories.

USER PROFILE:
${profileSummary}

TARGET CALORIE GOAL: ${calorieGoal} calories per day

AVAILABLE MEALS TO CHOOSE FROM:
${mealList}

Based on the user's calorie goal of ${calorieGoal} calories, recommend 6-8 meals from the available list that would help them achieve this target. Consider:
1. The total calories should align with their ${calorieGoal} calorie goal
2. Provide a good mix of breakfast, lunch, dinner, and snack options
3. Respect any dietary restrictions, allergies, or preferences
4. Ensure nutritional balance (protein, carbs, fats)
5. Consider meal frequency preferences

For each recommended meal, explain why it fits their calorie goal and provide portion guidance if needed.

Return your response in this JSON format:
{
  "recommendations": [
    {
      "mealName": "exact meal name from the list",
      "calories": meal_calories_number,
      "mealType": "breakfast/lunch/dinner/snack",
      "reason": "why this meal fits the calorie goal",
      "portionGuidance": "suggested portion size or modifications"
    }
  ],
  "personalizedMessage": "encouraging message about achieving their calorie goal",
  "nutritionalTips": [
    "tip 1 about meeting calorie goals",
    "tip 2 about balanced nutrition",
    "tip 3 about meal timing"
  ]
}

IMPORTANT: Only use meal names that appear EXACTLY in the provided list. Do not suggest any meals not in the list.
`;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate that all recommended meals exist in the database
      const validatedRecommendations = aiResponse.recommendations.filter(rec => {
        const mealExists = availableMeals.some(meal =>
          meal.name.toLowerCase() === rec.mealName.toLowerCase()
        );
        if (!mealExists) {
          console.warn(`AI recommended non-existent meal: ${rec.mealName}`);
        }
        return mealExists;
      });

      return {
        recommendations: validatedRecommendations,
        personalizedMessage: aiResponse.personalizedMessage || `Great choice setting a ${calorieGoal} calorie goal! Here are some meal recommendations to help you achieve it.`,
        nutritionalTips: aiResponse.nutritionalTips || [
          'Track your portions to stay within your calorie goal',
          'Include protein with each meal to maintain satiety',
          'Don\'t forget to stay hydrated throughout the day'
        ]
      };
    } catch (error) {
      console.error('Error generating calorie-based recommendations:', error);
      return {
        recommendations: [],
        personalizedMessage: `I've set your calorie goal to ${calorieGoal} calories. Let me help you find suitable meals!`,
        nutritionalTips: [
          'Focus on nutrient-dense foods to meet your calorie goal',
          'Include a variety of food groups for balanced nutrition',
          'Consider meal timing to optimize your energy levels'
        ]
      };
    }
  }

  // Create a comprehensive profile summary
  createProfileSummary(familyProfile) {
    const { userName, dietaryPreferences, familyMembers } = familyProfile;
    const hostName = userName || 'User';

    let summary = `
HOST USER: ${hostName}
DIETARY PREFERENCES FOR ${hostName.toUpperCase()}:
- Restrictions: ${dietaryPreferences?.restrictions?.join(', ') || 'None'}
- Allergies: ${dietaryPreferences?.allergies?.join(', ') || 'None'}
- Disliked Ingredients: ${dietaryPreferences?.dislikedIngredients?.join(', ') || 'None'}
- Daily Calorie Target: ${dietaryPreferences?.calorieTarget || 'Not specified'}
- Meals Per Day: ${dietaryPreferences?.mealFrequency || 3}
`;
    if (familyMembers && familyMembers.length > 0) {
      summary += `\n    FAMILY MEMBERS:`;
      familyMembers.forEach((member, index) => {
        const age = member.dateOfBirth ? this.calculateAge(member.dateOfBirth) : 'Unknown';
        summary += `\n    ${index + 1}. ${member.name} (Age: ${age})`;
        if (member.dietaryPreferences) {
          summary += `\n       - Restrictions: ${member.dietaryPreferences.restrictions?.join(', ') || 'None'}`;
          summary += `\n       - Allergies: ${member.dietaryPreferences.allergies?.join(', ') || 'None'}`;
          summary += `\n       - Calorie Target: ${member.dietaryPreferences.calorieTarget || 'Not specified'}`;
        }
      });
    }
    return summary;
  }

  // Helper function to calculate age
  calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  // Generate meal replacement recommendations
  async generateMealReplacement(originalMeal, familyProfile, availableMeals, mealType) {
    const profileSummary = this.createProfileSummary(familyProfile);

    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
       - Vegan: ${meal.dietType?.isVegan ? 'Yes' : 'No'}
       - Gluten-Free: ${meal.dietType?.isGlutenFree ? 'Yes' : 'No'}
       - Dairy-Free: ${meal.dietType?.isDairyFree ? 'Yes' : 'No'}
       - Nut-Free: ${meal.dietType?.isNutFree ? 'Yes' : 'No'}
       - Low-Carb: ${meal.dietType?.isLowCarb ? 'Yes' : 'No'}
       - Keto: ${meal.dietType?.isKeto ? 'Yes' : 'No'}
    `).join('');

    const prompt = `
You are a nutritionist AI helping replace a meal that no longer fits a family's dietary preferences.

FAMILY PROFILE:
${profileSummary}

MEAL TO REPLACE:
"${originalMeal.name}" (${mealType})
- Calories: ${originalMeal.calories}, Protein: ${originalMeal.protein}g, Carbs: ${originalMeal.carbs}g, Fat: ${originalMeal.fat}g
- Current dietary tags: ${originalMeal.dietaryTags?.join(', ') || 'None'}

AVAILABLE REPLACEMENT MEALS (you can ONLY choose from these):
${mealList}

TASK: Find the BEST replacement meal that:
1. Fits the family's NEW dietary preferences
2. Is similar in nutritional value to the original meal
3. Is appropriate for ${mealType}
4. Comes from the provided list ONLY

Return your response in this JSON format:
{
  "replacement": {
    "mealName": "EXACT meal name from the list",
    "reason": "why this is the best replacement",
    "nutritionalComparison": "how it compares nutritionally to the original",
    "suitability": "how it fits the new dietary preferences"
  },
  "alternatives": [
    {
      "mealName": "EXACT meal name from the list",
      "reason": "why this is also a good option"
    }
  ]
}

Only return the JSON, no additional text. Remember: ONLY use meal names that appear in the provided list.
`;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate that the recommended meal exists
      if (aiResponse.replacement) {
        const mealExists = availableMeals.some(meal =>
          meal.name.toLowerCase() === aiResponse.replacement.mealName.toLowerCase()
        );
        if (!mealExists) {
          console.warn(`AI recommended non-existent replacement meal: ${aiResponse.replacement.mealName}`);
          return null;
        }
      }

      return aiResponse;
    } catch (error) {
      console.error('Error generating meal replacement:', error);
      return null;
    }
  }

  // Helper function to extract JSON from markdown code blocks
  extractJsonFromResponse(response) {
    if (!response || typeof response !== 'string') {
      console.error('❌ Invalid response type:', typeof response);
      return '';
    }

    // Remove markdown code block markers
    let cleaned = response.replace(/```json\s*/gi, '').replace(/```\s*/g, '');

    // Remove any leading/trailing whitespace and newlines
    cleaned = cleaned.trim();

    // Try to find JSON object boundaries
    const jsonStart = cleaned.indexOf('{');
    const jsonEnd = cleaned.lastIndexOf('}');

    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
    } else {
      console.error('❌ No valid JSON object found in response');
      return '';
    }

    // Additional cleanup - remove any text before the first { or after the last }
    const lines = cleaned.split('\n');
    const cleanedLines = lines.filter(line => {
      const trimmed = line.trim();
      return trimmed === '' || trimmed.startsWith('{') || trimmed.startsWith('"') ||
             trimmed.startsWith('}') || trimmed.includes(':') || trimmed.includes('[') ||
             trimmed.includes(']') || trimmed.includes(',');
    });

    return cleanedLines.join('\n').trim();
  }

  // Helper function to repair truncated JSON responses
  repairTruncatedJson(truncatedJson) {
    try {
      // Check if it's a truncated mealAnalysis array
      if (truncatedJson.includes('"mealAnalysis"') && !truncatedJson.trim().endsWith(']}')) {
        let repaired = truncatedJson.trim();

        // Count open and close brackets to determine what's missing
        const openBraces = (repaired.match(/{/g) || []).length;
        const closeBraces = (repaired.match(/}/g) || []).length;
        const openBrackets = (repaired.match(/\[/g) || []).length;
        const closeBrackets = (repaired.match(/\]/g) || []).length;

        // Remove any incomplete object at the end
        const lastCompleteObject = repaired.lastIndexOf('},');
        if (lastCompleteObject > -1) {
          repaired = repaired.substring(0, lastCompleteObject + 1);
        }

        // Add missing closing brackets and braces
        const missingCloseBraces = openBraces - closeBraces;
        const missingCloseBrackets = openBrackets - closeBrackets;

        for (let i = 0; i < missingCloseBraces; i++) {
          repaired += '}';
        }
        for (let i = 0; i < missingCloseBrackets; i++) {
          repaired += ']';
        }

        console.log('🔧 Attempting to repair JSON:', repaired);
        return repaired;
      }
    } catch (error) {
      console.error('❌ Error repairing JSON:', error);
    }
    return null;
  }

  // Generate a 7-day weekly meal plan with smart diversity management
  async generateWeeklyAIMealPlan(familyProfile, availableMeals) {
    const profileSummary = this.createProfileSummary(familyProfile);
    const totalMealsNeeded = 21; // 7 days × 3 main meals (breakfast, lunch, dinner)
    const availableMealCount = availableMeals.length;

    console.log(`📊 Weekly meal plan generation: ${availableMealCount} meals available, ${totalMealsNeeded} meals needed`);

    // Determine diversity strategy based on available meals
    let diversityStrategy;
    let allowDuplication = false;

    if (availableMealCount >= totalMealsNeeded * 1.5) {
      diversityStrategy = "MAXIMUM_DIVERSITY";
      allowDuplication = false;
      console.log("🎯 Strategy: MAXIMUM_DIVERSITY - No duplication allowed");
    } else if (availableMealCount >= totalMealsNeeded) {
      diversityStrategy = "HIGH_DIVERSITY";
      allowDuplication = false;
      console.log("🎯 Strategy: HIGH_DIVERSITY - Minimal duplication if needed");
    } else {
      diversityStrategy = "STRATEGIC_DUPLICATION";
      allowDuplication = true;
      console.log("🎯 Strategy: STRATEGIC_DUPLICATION - Smart duplication allowed");
    }

    // Create a focused meal list for the AI
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories || 'N/A'}, Protein: ${meal.protein || 0}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'Any'}
       - Dietary: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
    `).join('');

    const hostName = familyProfile.userName || 'User';

    // Create dynamic duplication rules based on strategy
    const duplicationRules = allowDuplication ?
      `DUPLICATION STRATEGY (${availableMealCount} meals available for ${totalMealsNeeded} needed):
- Meals can be repeated strategically to fill all 21 slots
- Space duplicates across different days (e.g., Day 1 and Day 5)
- Vary the meal types when duplicating (breakfast vs lunch vs dinner)
- Prioritize variety in main meals over perfect uniqueness
- Ensure each day still has diverse meal types` :
      `DIVERSITY STRATEGY (${availableMealCount} meals available for ${totalMealsNeeded} needed):
- Maximize variety - avoid duplication when possible
- Each meal slot should ideally have a unique meal
- Focus on nutritional balance and variety across the week`;

    const prompt = `
You are a nutritionist AI creating a complete 7-day weekly meal plan for a Filipino family.

MEAL AVAILABILITY: ${availableMealCount} meals available for ${totalMealsNeeded} meal slots needed
STRATEGY: ${diversityStrategy}

FAMILY PROFILE:
${profileSummary}

${duplicationRules}

AVAILABLE MEALS IN DATABASE:
${mealList}

CRITICAL REQUIREMENTS:
1. MUST create exactly 21 meals total (7 days × 3 meals per day)
2. ONLY use meal names that appear EXACTLY in the provided list
3. Consider dietary restrictions and allergies for ALL family members
4. Each day must have: 1 breakfast + 1 lunch + 1 dinner
5. If duplication is needed, space it strategically across days
6. Ensure nutritional balance across the entire week

MEAL ASSIGNMENT RULES:
- Breakfast: Light, energizing meals to start the day
- Lunch: Balanced, substantial meals for midday energy
- Dinner: Satisfying, nutritious meals to end the day

Return your response in this EXACT JSON format:
{
  "weeklyMealPlan": {
    "day1": {
      "breakfast": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "lunch": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "dinner": [{"mealName": "exact meal name from list", "reason": "brief reason"}]
    },
    "day2": {
      "breakfast": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "lunch": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "dinner": [{"mealName": "exact meal name from list", "reason": "brief reason"}]
    },
    "day3": {
      "breakfast": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "lunch": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "dinner": [{"mealName": "exact meal name from list", "reason": "brief reason"}]
    },
    "day4": {
      "breakfast": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "lunch": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "dinner": [{"mealName": "exact meal name from list", "reason": "brief reason"}]
    },
    "day5": {
      "breakfast": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "lunch": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "dinner": [{"mealName": "exact meal name from list", "reason": "brief reason"}]
    },
    "day6": {
      "breakfast": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "lunch": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "dinner": [{"mealName": "exact meal name from list", "reason": "brief reason"}]
    },
    "day7": {
      "breakfast": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "lunch": [{"mealName": "exact meal name from list", "reason": "brief reason"}],
      "dinner": [{"mealName": "exact meal name from list", "reason": "brief reason"}]
    }
  },
  "nutritionalSummary": "Brief summary of nutritional balance across the week",
  "personalizedMessage": "Hello ${hostName}! Brief explanation of why this meal plan was created for your family",
  "diversityAnalysis": "Brief explanation of how variety was achieved with the available meals"
}

IMPORTANT: Return ONLY the JSON, no additional text. Ensure all 21 meal slots are filled.
`;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate and intelligently handle meal plan with smart duplication management
      const validateWeeklyMealPlan = (weeklyMealPlan) => {
        const validatedPlan = {};
        const mealUsageCount = new Map(); // Track how many times each meal is used
        let duplicatesFound = [];
        let invalidMealsFound = [];

        // Initialize all days with empty meal types
        ['day1', 'day2', 'day3', 'day4', 'day5', 'day6', 'day7'].forEach(day => {
          validatedPlan[day] = {
            breakfast: [],
            lunch: [],
            dinner: []
          };
        });

        // First pass: validate meal existence and track usage
        ['day1', 'day2', 'day3', 'day4', 'day5', 'day6', 'day7'].forEach(day => {
          if (weeklyMealPlan[day]) {
            ['breakfast', 'lunch', 'dinner'].forEach(mealType => {
              if (weeklyMealPlan[day][mealType] && weeklyMealPlan[day][mealType].length > 0) {
                const mealItem = weeklyMealPlan[day][mealType][0]; // Take first meal
                const mealExists = availableMeals.some(meal =>
                  meal.name.toLowerCase() === mealItem.mealName.toLowerCase()
                );

                if (!mealExists) {
                  console.warn(`❌ AI recommended non-existent meal: ${mealItem.mealName}`);
                  invalidMealsFound.push(mealItem.mealName);
                } else {
                  // Track meal usage
                  const mealNameLower = mealItem.mealName.toLowerCase();
                  const currentCount = mealUsageCount.get(mealNameLower) || 0;
                  mealUsageCount.set(mealNameLower, currentCount + 1);

                  // Allow strategic duplication based on available meals
                  const maxAllowedDuplicates = availableMealCount < totalMealsNeeded ?
                    Math.ceil(totalMealsNeeded / availableMealCount) : 1;

                  if (currentCount < maxAllowedDuplicates) {
                    validatedPlan[day][mealType] = [mealItem];
                  } else {
                    duplicatesFound.push(`${mealItem.mealName} (${currentCount + 1} times)`);
                    console.warn(`⚠️ Excessive duplication: ${mealItem.mealName} used ${currentCount + 1} times`);
                  }
                }
              }
            });
          }
        });

        // Fill empty meal slots with intelligent fallback
        const fillEmptySlots = () => {
          let availableMealsForFallback = [...availableMeals];
          let emptySlots = [];

          // Count empty slots
          ['day1', 'day2', 'day3', 'day4', 'day5', 'day6', 'day7'].forEach(day => {
            ['breakfast', 'lunch', 'dinner'].forEach(mealType => {
              if (validatedPlan[day][mealType].length === 0) {
                emptySlots.push({ day, mealType });
              }
            });
          });

          console.log(`🔧 Filling ${emptySlots.length} empty meal slots`);

          // Fill empty slots with strategic meal selection
          emptySlots.forEach(slot => {
            if (availableMealsForFallback.length > 0) {
              // Try to find a meal appropriate for this meal type
              let selectedMeal = availableMealsForFallback.find(meal =>
                meal.mealType && meal.mealType.includes(slot.mealType)
              );

              // If no appropriate meal type found, use any available meal
              if (!selectedMeal) {
                selectedMeal = availableMealsForFallback[0];
              }

              if (selectedMeal) {
                validatedPlan[slot.day][slot.mealType] = [{
                  mealName: selectedMeal.name,
                  reason: `Selected to complete your ${slot.mealType} for ${slot.day}`
                }];

                // Check if we should remove this meal from available list (to avoid overuse)
                const currentUsage = mealUsageCount.get(selectedMeal.name.toLowerCase()) || 0;
                const maxUsage = availableMealCount < totalMealsNeeded ?
                  Math.ceil(totalMealsNeeded / availableMealCount) : 2;

                if (currentUsage >= maxUsage) {
                  const index = availableMealsForFallback.indexOf(selectedMeal);
                  if (index > -1) availableMealsForFallback.splice(index, 1);
                }

                // Update usage count
                mealUsageCount.set(selectedMeal.name.toLowerCase(), currentUsage + 1);
              }
            }
          });
        };

        fillEmptySlots();

        // Log results
        if (duplicatesFound.length > 0) {
          console.warn(`⚠️ Excessive duplicates handled: ${duplicatesFound.join(', ')}`);
        }
        if (invalidMealsFound.length > 0) {
          console.warn(`❌ Invalid meals removed: ${invalidMealsFound.join(', ')}`);
        }

        // Count final meals
        let totalMealsInPlan = 0;
        ['day1', 'day2', 'day3', 'day4', 'day5', 'day6', 'day7'].forEach(day => {
          ['breakfast', 'lunch', 'dinner'].forEach(mealType => {
            if (validatedPlan[day][mealType].length > 0) {
              totalMealsInPlan++;
            }
          });
        });

        console.log(`✅ Final meal plan: ${totalMealsInPlan}/${totalMealsNeeded} meals assigned`);
        return validatedPlan;
      };

      const validatedWeeklyMealPlan = validateWeeklyMealPlan(aiResponse.weeklyMealPlan);

      return {
        weeklyMealPlan: validatedWeeklyMealPlan,
        nutritionalSummary: aiResponse.nutritionalSummary || 'Balanced nutrition across the week with variety in proteins, carbohydrates, and vegetables.',
        personalizedMessage: aiResponse.personalizedMessage || `Hello ${hostName}! Here's your personalized 7-day meal plan created based on your family's dietary preferences and available meals.`,
        diversityAnalysis: aiResponse.diversityAnalysis || `Variety achieved using ${availableMealCount} available meals with strategic meal selection and ${allowDuplication ? 'smart duplication' : 'maximum diversity'}.`
      };
    } catch (error) {
      console.error('Error generating weekly AI meal plan:', error);
      return {
        weeklyMealPlan: {
          day1: { breakfast: [], lunch: [], dinner: [], snacks: [] },
          day2: { breakfast: [], lunch: [], dinner: [], snacks: [] },
          day3: { breakfast: [], lunch: [], dinner: [], snacks: [] },
          day4: { breakfast: [], lunch: [], dinner: [], snacks: [] },
          day5: { breakfast: [], lunch: [], dinner: [], snacks: [] },
          day6: { breakfast: [], lunch: [], dinner: [], snacks: [] },
          day7: { breakfast: [], lunch: [], dinner: [], snacks: [] }
        },
        nutritionalSummary: '',
        personalizedMessage: 'I\'m sorry, I had trouble creating your weekly meal plan. Please try again.',
        diversityAnalysis: ''
      };
    }
  }

  // Generate a complete meal plan for a specific date
  async generateAIMealPlan(familyProfile, availableMeals, targetDate = null) {
    const profileSummary = this.createProfileSummary(familyProfile);

    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
       - Vegan: ${meal.dietType?.isVegan ? 'Yes' : 'No'}
       - Gluten-Free: ${meal.dietType?.isGlutenFree ? 'Yes' : 'No'}
       - Dairy-Free: ${meal.dietType?.isDairyFree ? 'Yes' : 'No'}
       - Nut-Free: ${meal.dietType?.isNutFree ? 'Yes' : 'No'}
       - Low-Carb: ${meal.dietType?.isLowCarb ? 'Yes' : 'No'}
       - Keto: ${meal.dietType?.isKeto ? 'Yes' : 'No'}
       - Pescatarian: ${meal.dietType?.isPescatarian ? 'Yes' : 'No'}
       - Halal: ${meal.dietType?.isHalal ? 'Yes' : 'No'}
    `).join('');

    const hostName = familyProfile.userName || 'User';

    const prompt = `
You are a nutritionist AI creating a complete daily meal plan for a Filipino family. You MUST ONLY use meals from the exact list provided below.

FAMILY PROFILE:
${profileSummary}

AVAILABLE MEALS IN DATABASE (you can ONLY choose from these):
${mealList}

Create a balanced daily meal plan with:
- 1-2 breakfast options
- 1-2 lunch options
- 1-2 dinner options
- 1-2 snack options

IMPORTANT REQUIREMENTS:
1. ONLY use meal names that appear EXACTLY in the provided list
2. Consider BOTH the host user (${hostName}) AND family members' dietary restrictions and allergies
3. Ensure nutritional balance across the day
4. Respect calorie targets if specified
5. Choose meals appropriate for each meal type (breakfast, lunch, dinner, snacks)
6. Provide variety and flavor balance
7. For snacks, choose lighter options or items that work well as snacks
8. If there are dietary conflicts between family members, prioritize the host user (${hostName}) but mention the conflicts

Return your response in this JSON format:
{
  "mealPlan": {
    "breakfast": [
      {
        "mealName": "exact meal name from list",
        "reason": "why this meal is good for breakfast and fits their preferences"
      }
    ],
    "lunch": [
      {
        "mealName": "exact meal name from list",
        "reason": "why this meal is good for lunch and fits their preferences"
      }
    ],
    "dinner": [
      {
        "mealName": "exact meal name from list",
        "reason": "why this meal is good for dinner and fits their preferences"
      }
    ],
    "snacks": [
      {
        "mealName": "exact meal name from list",
        "reason": "why this meal is good as a snack and fits their preferences"
      }
    ]
  },
  "nutritionalSummary": "brief summary of the nutritional balance",
  "personalizedMessage": "Start with 'Hello ${hostName}!' and explain why this meal plan was created for them and their family"
}

Only return the JSON, no additional text. Remember: ONLY use meal names that appear in the provided list.
`;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate that all recommended meals exist in the database
      const validateMealPlan = (mealPlan) => {
        const validatedPlan = {};

        ['breakfast', 'lunch', 'dinner', 'snacks'].forEach(mealType => {
          if (mealPlan[mealType]) {
            validatedPlan[mealType] = mealPlan[mealType].filter(mealItem => {
              const mealExists = availableMeals.some(meal =>
                meal.name.toLowerCase() === mealItem.mealName.toLowerCase()
              );
              if (!mealExists) {
                console.warn(`AI recommended non-existent meal: ${mealItem.mealName}`);
              }
              return mealExists;
            });
          }
        });

        return validatedPlan;
      };

      const validatedMealPlan = validateMealPlan(aiResponse.mealPlan);

      return {
        mealPlan: validatedMealPlan,
        nutritionalSummary: aiResponse.nutritionalSummary || '',
        personalizedMessage: aiResponse.personalizedMessage || 'Here\'s your personalized meal plan!'
      };
    } catch (error) {
      console.error('Error generating AI meal plan:', error);
      return {
        mealPlan: { breakfast: [], lunch: [], dinner: [], snacks: [] },
        nutritionalSummary: '',
        personalizedMessage: 'I\'m sorry, I had trouble creating your meal plan. Please try again.'
      };
    }
  }

  // Aggregate family preferences and detect conflicts
  aggregateFamilyPreferences(familyProfile) {
    const { dietaryPreferences: userPrefs, familyMembers } = familyProfile;

    // Collect all preferences
    const allRestrictions = [...(userPrefs?.restrictions || [])];
    const allAllergies = [...(userPrefs?.allergies || [])];
    const allDislikedIngredients = [...(userPrefs?.dislikedIngredients || [])];
    const calorieTargets = [];

    // Add user's calorie target if available
    if (userPrefs?.calorieTarget) {
      calorieTargets.push(userPrefs.calorieTarget);
    }

    // Aggregate family member preferences
    familyMembers.forEach(member => {
      if (member.dietaryPreferences) {
        allRestrictions.push(...(member.dietaryPreferences.restrictions || []));
        allAllergies.push(...(member.dietaryPreferences.allergies || []));
        allDislikedIngredients.push(...(member.dietaryPreferences.dislikedIngredients || []));

        if (member.dietaryPreferences.calorieTarget) {
          calorieTargets.push(member.dietaryPreferences.calorieTarget);
        }
      }
    });

    // Remove duplicates and calculate average calorie target
    const uniqueRestrictions = [...new Set(allRestrictions)];
    const uniqueAllergies = [...new Set(allAllergies)];
    const uniqueDislikedIngredients = [...new Set(allDislikedIngredients)];
    const averageCalorieTarget = calorieTargets.length > 0
      ? Math.round(calorieTargets.reduce((sum, cal) => sum + cal, 0) / calorieTargets.length)
      : 2000; // Default if no targets specified

    // Detect conflicts between dietary restrictions
    const conflicts = [];
    const conflictingPairs = [
      ['Vegetarian', 'Pescatarian'],
      ['Vegan', 'Vegetarian'],
      ['Vegan', 'Pescatarian'],
      ['Keto', 'Low-Carb'], // Not really a conflict, but worth noting
    ];

    conflictingPairs.forEach(([restriction1, restriction2]) => {
      if (uniqueRestrictions.includes(restriction1) && uniqueRestrictions.includes(restriction2)) {
        conflicts.push(`Family has both ${restriction1} and ${restriction2} preferences`);
      }
    });

    // Check for allergy-restriction conflicts
    if (uniqueAllergies.includes('Milk') && uniqueRestrictions.includes('Vegetarian')) {
      conflicts.push('Milk allergy detected with Vegetarian preference - will prioritize dairy-free options');
    }

    return {
      aggregatedPreferences: {
        restrictions: uniqueRestrictions,
        allergies: uniqueAllergies,
        dislikedIngredients: uniqueDislikedIngredients,
        calorieTarget: averageCalorieTarget
      },
      conflicts,
      familySize: familyMembers.length + 1 // +1 for the user
    };
  }

  // Generate family meal plan with conflict detection
  async generateFamilyMealPlan(familyProfile, availableMeals, targetDate = null) {
    const { aggregatedPreferences, conflicts, familySize } = this.aggregateFamilyPreferences(familyProfile);

    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
    `).join('\n');

    const conflictInfo = conflicts.length > 0
      ? `\n\nIMPORTANT DIETARY CONFLICTS DETECTED:\n${conflicts.join('\n')}\nPlease accommodate these conflicts in your meal selection.`
      : '';

    const prompt = `
You are a Filipino meal planning assistant creating a family meal plan for ${familySize} people.

FAMILY DIETARY PROFILE:
- Dietary Restrictions: ${aggregatedPreferences.restrictions.join(', ') || 'None'}
- Allergies: ${aggregatedPreferences.allergies.join(', ') || 'None'}
- Disliked Ingredients: ${aggregatedPreferences.dislikedIngredients.join(', ') || 'None'}
- Average Daily Calorie Target: ${aggregatedPreferences.calorieTarget} calories
- Family Size: ${familySize} people${conflictInfo}

AVAILABLE MEALS TO CHOOSE FROM:
${mealList}

INSTRUCTIONS:
1. Create a balanced daily meal plan with breakfast, lunch, dinner, and snacks
2. ONLY select meals from the provided list above
3. STRICTLY follow ALL family dietary restrictions and allergies - NO EXCEPTIONS
4. If family has VEGAN restrictions, NEVER include any animal products (meat, dairy, eggs, fish)
5. If family has VEGETARIAN restrictions, avoid meat and fish but dairy/eggs may be okay unless allergies exist
6. NEVER include meals with allergens listed in the family profile
7. Aim for the average calorie target across all meals
8. Provide variety in meal types and nutritional balance
9. If conflicts exist, prioritize safety (allergies) over preferences, then choose the most restrictive diet
10. Include 1-2 meals per meal type (breakfast, lunch, dinner, snacks)
11. For snacks, choose lighter options or items that work well as snacks

Return your response in this EXACT JSON format:
{
  "mealPlan": {
    "breakfast": [
      {
        "mealName": "Exact meal name from the list",
        "reason": "Why this meal works for the family (dietary compatibility, nutrition, etc.)"
      }
    ],
    "lunch": [
      {
        "mealName": "Exact meal name from the list",
        "reason": "Why this meal works for the family"
      }
    ],
    "dinner": [
      {
        "mealName": "Exact meal name from the list",
        "reason": "Why this meal works for the family"
      }
    ],
    "snacks": [
      {
        "mealName": "Exact meal name from the list",
        "reason": "Why this meal works as a snack for the family"
      }
    ]
  },
  "personalizedMessage": "A warm, personalized message about this family meal plan",
  "nutritionalSummary": "Brief summary of nutritional benefits and how it meets family needs"
}
`;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate that all recommended meals exist in the database
      const validateMealPlan = (mealPlan) => {
        const validatedPlan = {};

        ['breakfast', 'lunch', 'dinner', 'snacks'].forEach(mealType => {
          if (mealPlan[mealType]) {
            validatedPlan[mealType] = mealPlan[mealType].filter(mealItem => {
              const mealExists = availableMeals.some(meal =>
                meal.name.toLowerCase() === mealItem.mealName.toLowerCase()
              );
              if (!mealExists) {
                console.warn(`AI recommended non-existent meal: ${mealItem.mealName}`);
              }
              return mealExists;
            });
          }
        });

        return validatedPlan;
      };

      const validatedMealPlan = validateMealPlan(aiResponse.mealPlan);

      return {
        mealPlan: validatedMealPlan,
        nutritionalSummary: aiResponse.nutritionalSummary || '',
        personalizedMessage: aiResponse.personalizedMessage || 'Here\'s your personalized family meal plan!',
        conflicts: conflicts
      };
    } catch (error) {
      console.error('Error generating family meal plan:', error);
      return {
        mealPlan: { breakfast: [], lunch: [], dinner: [], snacks: [] },
        nutritionalSummary: '',
        personalizedMessage: 'I\'m sorry, I had trouble creating your family meal plan. Please try again.',
        conflicts: conflicts
      };
    }
  }

  // Generate chat response for general queries
  async generateChatResponse(message, context = {}) {
    let availableMealsContext = '';
    // If context includes available meals, add them to the prompt
    if (context.availableMeals && context.availableMeals.length > 0) {
      const mealNames = context.availableMeals.map(meal => meal.name).join(', ');
      availableMealsContext = `\n\nAvailable meals in our database: ${mealNames}`;
    }
    const hostName = context.familyProfile?.userName || 'User';

    const prompt = `
You are a helpful Filipino meal planning assistant. The user has sent you this message: "${message}"
${context.familyProfile ? `User's Family Profile: ${this.createProfileSummary(context.familyProfile)}` : ''}
${availableMealsContext}
Provide a helpful, friendly response related to meal planning, nutrition, or Filipino cuisine. Keep your response concise and actionable.
IMPORTANT:
1. Always address the host user by name (${hostName}) in your response
2. If the user asks about meal recommendations or specific dishes, ONLY mention meals that are available in our database (listed above)
3. Consider both the host user (${hostName}) and family members' preferences when making recommendations
4. If the user is asking about meal recommendations, dietary advice, or nutrition, provide specific suggestions from our available meals
5. If the user is asking about Filipino dishes, provide cultural context and preparation tips, but focus on dishes we have available
6. If there are dietary conflicts between family members, prioritize the host user (${hostName}) but mention the considerations for family members
Return your response as plain text, maximum 200 words.
`;
    try {
      const response = await this.generateContent(prompt);
      return response;
    } catch (error) {
      console.error('Error generating chat response:', error);
      return "I'm sorry, I'm having trouble responding right now. Please try again later.";
    }
  }

  // Filter meals based on dietary preferences
  filterMealsByDietaryPreferences(availableMeals, dietaryPreferences) {
    if (!dietaryPreferences || !availableMeals || availableMeals.length === 0) {
      return availableMeals || [];
    }

    return availableMeals.filter(meal => {
      // Check dietary restrictions (inclusive filtering - user wants ONLY these types)
      if (dietaryPreferences.restrictions && dietaryPreferences.restrictions.length > 0) {
        const dietType = meal.dietType || {};
        const hasRequiredRestriction = dietaryPreferences.restrictions.some(restriction => {
          switch (restriction) {
            case 'Vegetarian':
              return dietType.isVegetarian === true;
            case 'Vegan':
              return dietType.isVegan === true;
            case 'Gluten-Free':
              return dietType.isGlutenFree === true;
            case 'Dairy-Free':
              return dietType.isDairyFree === true;
            case 'Nut-Free':
              return dietType.isNutFree === true;
            case 'Low-Carb':
              return dietType.isLowCarb === true;
            case 'Keto':
              return dietType.isKeto === true;
            case 'High-Protein':
              return dietType.isHighProtein === true;
            case 'Low-Sodium':
              return dietType.isLowSodium === true;
            case 'Halal':
              return dietType.isHalal === true;
            case 'Pescatarian':
              return dietType.isPescatarian === true;
            default:
              return false;
          }
        });

        if (!hasRequiredRestriction) {
          return false;
        }
      }

      // Check allergies (exclusive filtering - user does NOT want these)
      if (dietaryPreferences.allergies && dietaryPreferences.allergies.length > 0) {
        const mealAllergens = meal.allergens || [];
        const mealIngredients = meal.ingredients || [];

        const hasAllergen = dietaryPreferences.allergies.some(allergen => {
          // Check if allergen is in the meal's allergen list
          if (mealAllergens.some(mealAllergen =>
            mealAllergen.toLowerCase().includes(allergen.toLowerCase())
          )) {
            return true;
          }

          // Check if allergen is in the ingredients
          if (mealIngredients.some(ingredient =>
            ingredient.toLowerCase().includes(allergen.toLowerCase())
          )) {
            return true;
          }

          return false;
        });

        if (hasAllergen) {
          return false;
        }
      }

      // Check disliked ingredients (exclusive filtering)
      if (dietaryPreferences.dislikedIngredients && dietaryPreferences.dislikedIngredients.length > 0) {
        const mealIngredients = meal.ingredients || [];

        const hasDislikedIngredient = dietaryPreferences.dislikedIngredients.some(disliked => {
          return mealIngredients.some(ingredient =>
            ingredient.toLowerCase().includes(disliked.toLowerCase())
          );
        });

        if (hasDislikedIngredient) {
          return false;
        }
      }

      return true;
    });
  }

  // Filter meals based on dietary preferences
  filterMealsByDietaryPreferences(availableMeals, dietaryPreferences) {
    if (!dietaryPreferences || !availableMeals || availableMeals.length === 0) {
      return availableMeals || [];
    }

    return availableMeals.filter(meal => {
      // Check dietary restrictions (inclusive filtering - user wants ONLY these types)
      if (dietaryPreferences.restrictions && dietaryPreferences.restrictions.length > 0) {
        const dietType = meal.dietType || {};
        const hasRequiredRestriction = dietaryPreferences.restrictions.some(restriction => {
          switch (restriction) {
            case 'Vegetarian':
              return dietType.isVegetarian === true;
            case 'Vegan':
              return dietType.isVegan === true;
            case 'Gluten-Free':
              return dietType.isGlutenFree === true;
            case 'Dairy-Free':
              return dietType.isDairyFree === true;
            case 'Nut-Free':
              return dietType.isNutFree === true;
            case 'Low-Carb':
              return dietType.isLowCarb === true;
            case 'Keto':
              return dietType.isKeto === true;
            case 'High-Protein':
              return dietType.isHighProtein === true;
            case 'Low-Sodium':
              return dietType.isLowSodium === true;
            case 'Halal':
              return dietType.isHalal === true;
            case 'Pescatarian':
              return dietType.isPescatarian === true;
            default:
              return false;
          }
        });

        if (!hasRequiredRestriction) {
          return false;
        }
      }

      // Check allergies (exclusive filtering - user does NOT want these)
      if (dietaryPreferences.allergies && dietaryPreferences.allergies.length > 0) {
        const mealAllergens = meal.allergens || [];
        const mealIngredients = meal.ingredients || [];

        const hasAllergen = dietaryPreferences.allergies.some(allergen => {
          // Check if allergen is in the meal's allergen list
          if (mealAllergens.some(mealAllergen =>
            mealAllergen.toLowerCase().includes(allergen.toLowerCase())
          )) {
            return true;
          }

          // Check if allergen is in the ingredients
          if (mealIngredients.some(ingredient =>
            ingredient.toLowerCase().includes(allergen.toLowerCase())
          )) {
            return true;
          }

          return false;
        });

        if (hasAllergen) {
          return false;
        }
      }

      // Check disliked ingredients (exclusive filtering)
      if (dietaryPreferences.dislikedIngredients && dietaryPreferences.dislikedIngredients.length > 0) {
        const mealIngredients = meal.ingredients || [];

        const hasDislikedIngredient = dietaryPreferences.dislikedIngredients.some(disliked => {
          return mealIngredients.some(ingredient =>
            ingredient.toLowerCase().includes(disliked.toLowerCase())
          );
        });

        if (hasDislikedIngredient) {
          return false;
        }
      }

      return true;
    });
  }

  // Edit existing meal plan based on user request
  async editMealPlan(userProfile, availableMeals, currentMealPlan, editRequest) {
    try {
      const { dietaryPreferences, familyMembers } = userProfile;

      // Handle case where dietaryPreferences might be an array (legacy format)
      let normalizedDietaryPreferences;

      if (Array.isArray(dietaryPreferences)) {
        // Legacy format: dietaryPreferences is an array of strings
        normalizedDietaryPreferences = {
          restrictions: dietaryPreferences,
          allergies: [],
          dislikedIngredients: [],
          calorieTarget: 2000,
          mealFrequency: 3
        };
      } else if (dietaryPreferences && typeof dietaryPreferences === 'object') {
        // New format: dietaryPreferences is an object
        normalizedDietaryPreferences = {
          restrictions: Array.isArray(dietaryPreferences.restrictions) ? dietaryPreferences.restrictions : [],
          allergies: Array.isArray(dietaryPreferences.allergies) ? dietaryPreferences.allergies : [],
          dislikedIngredients: Array.isArray(dietaryPreferences.dislikedIngredients) ? dietaryPreferences.dislikedIngredients : [],
          calorieTarget: dietaryPreferences.calorieTarget || 2000,
          mealFrequency: dietaryPreferences.mealFrequency || 3
        };
      } else {
        // No dietary preferences
        normalizedDietaryPreferences = {
          restrictions: [],
          allergies: [],
          dislikedIngredients: [],
          calorieTarget: 2000,
          mealFrequency: 3
        };
      }

      // Filter meals based on dietary preferences
      const filteredMeals = this.filterMealsByDietaryPreferences(availableMeals, normalizedDietaryPreferences);

      // Create meal options string
      const mealOptions = filteredMeals.map(meal =>
        `${meal.name} (${meal.dietType || 'General'}) - ₱${meal.price} - ${meal.description || 'No description'}`
      ).join('\n');

      const hostName = userProfile.userName || 'User';

      const prompt = `You are a Filipino meal planning assistant. A user wants to edit their current meal plan.

CURRENT MEAL PLAN:
${JSON.stringify(currentMealPlan, null, 2)}

USER'S EDIT REQUEST:
${editRequest}

HOST USER: ${hostName}
USER DIETARY PREFERENCES FOR ${hostName.toUpperCase()}:
- Restrictions: ${normalizedDietaryPreferences.restrictions.join(', ') || 'None'}
- Allergies: ${normalizedDietaryPreferences.allergies.join(', ') || 'None'}
- Disliked Ingredients: ${normalizedDietaryPreferences.dislikedIngredients.join(', ') || 'None'}
- Daily Calorie Target: ${normalizedDietaryPreferences.calorieTarget || 'Not specified'}
- Meals Per Day: ${normalizedDietaryPreferences.mealFrequency || 3}

${familyMembers && familyMembers.length > 0 ? `
FAMILY MEMBERS:
${familyMembers.map(member => `- ${member.name} (${member.relationship}):
  Restrictions: ${member.dietaryPreferences?.restrictions?.join(', ') || 'None'}
  Allergies: ${member.dietaryPreferences?.allergies?.join(', ') || 'None'}`).join('\n')}
` : ''}

AVAILABLE MEALS TO CHOOSE FROM:
${mealOptions}

INSTRUCTIONS FOR EDITING:
1. Carefully analyze the user's edit request - they may use phrases like "I want to replace X with Y", "Replace X with Y", "Change X to Y", etc.
2. Extract the meal they want to replace and what they want to replace it with
3. Find the EXACT meal names from the available meals list above - search for the requested meal name in the list
4. If the requested replacement meal is not in the available list, respond with an error message explaining that the meal is not available
5. Keep all other meals in the plan unchanged unless specifically requested
6. Ensure the replacement meal fits the same meal type (breakfast/lunch/dinner)
7. Respect all dietary preferences and restrictions
8. NEVER return undefined, null, or empty values for any meal properties

CRITICAL REQUIREMENTS - FOLLOW THESE EXACTLY:
- ONLY use meals from the available meals list above
- Use EXACT meal names as they appear in the list
- If you cannot find the exact meal requested, return an error message instead of guessing
- If a meal is not being replaced, copy it exactly from the current meal plan with mealName and reason properties
- Always provide a clear reason for each meal selection

MEAL MATCHING PROCESS:
1. Search for EXACT match of requested meal name in available meals list
2. If exact match found → Use that meal
3. If NO exact match found → Return error message: "I'm sorry, but [meal name] is not available in our database. Please choose from the available meals or try a different meal name."

STEP-BY-STEP PROCESS:
1. Identify which meal the user wants to replace (e.g., "Sinangag")
2. Identify what they want to replace it with (e.g., "Chicken Curry")
3. Find the exact meal in the current meal plan that matches the meal to replace
4. Look for the EXACT replacement meal in the available meals list
5. If replacement meal exists: Replace ONLY that specific meal, keep all other meals exactly the same
6. If replacement meal does NOT exist: Return error message explaining the meal is not available

Return your response in this exact JSON format:

FOR SUCCESSFUL EDITS:
{
  "mealPlan": {
    "breakfast": [{"mealName": "EXACT meal name from available meals", "reason": "why this meal is good for breakfast and fits their preferences"}],
    "lunch": [{"mealName": "EXACT meal name from available meals", "reason": "why this meal is good for lunch and fits their preferences"}],
    "dinner": [{"mealName": "EXACT meal name from available meals", "reason": "why this meal is good for dinner and fits their preferences"}]
  },
  "nutritionalSummary": "Brief nutritional overview of the updated plan",
  "personalizedMessage": "Start with 'Hello ${hostName}!' and provide friendly explanation of what changes were made and why (e.g., 'Hello ${hostName}! I've replaced [old meal] with [new meal] as requested')",
  "totalCost": 0
}

FOR MEAL NOT FOUND ERRORS:
{
  "error": true,
  "personalizedMessage": "Hello ${hostName}! I'm sorry, but [requested meal name] is not available in our database. Please choose from the available meals or try a different meal name."
}

EXAMPLE RESPONSE:
If user says "I want to replace Sinangag with Chicken Curry in breakfast":
- Find "Sinangag" in the current breakfast meals
- Look for "Chicken Curry" in available meals list
- Replace only that meal, keep "Longganisa" unchanged
- Your personalizedMessage should be: "Hello ${hostName}! I've replaced Sinangag with Chicken Curry as requested. This adds a flavorful and protein-rich option to your breakfast while keeping the plan halal and dairy-free."`;

      const result = await this.generateContent(prompt);
      console.log('🤖 AI Raw Response:', result);

      // Parse the JSON response
      const jsonMatch = result.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.error('❌ No valid JSON found in AI response:', result);
        throw new Error('No valid JSON found in AI response');
      }

      console.log('📝 Extracted JSON:', jsonMatch[0]);
      const parsedResult = JSON.parse(jsonMatch[0]);
      console.log('🔍 Parsed Result:', JSON.stringify(parsedResult, null, 2));

      // Check if AI returned an error message (meal not found)
      if (parsedResult.error || parsedResult.personalizedMessage?.includes("not available in our database")) {
        return {
          mealPlan: currentMealPlan,
          personalizedMessage: parsedResult.personalizedMessage || parsedResult.error || "I'm sorry, the requested meal is not available in our database. Please choose from the available meals.",
          nutritionalSummary: "Original meal plan maintained",
          totalCost: 0
        };
      }

      // Validate the response structure
      if (!parsedResult.mealPlan || !parsedResult.personalizedMessage) {
        console.error('❌ Invalid meal plan structure in AI response:', parsedResult);
        // Return the original meal plan with an error message
        return {
          mealPlan: currentMealPlan,
          personalizedMessage: "I'm sorry, I had trouble processing your edit request. The original meal plan has been preserved. Please try rephrasing your request.",
          nutritionalSummary: "Original meal plan maintained",
          totalCost: 0
        };
      }

      // Validate each meal in the plan to prevent undefined values
      const mealTypes = ['breakfast', 'lunch', 'dinner'];
      for (const mealType of mealTypes) {
        if (parsedResult.mealPlan[mealType]) {
          console.log(`🔍 Validating ${mealType} meals:`, parsedResult.mealPlan[mealType]);

          parsedResult.mealPlan[mealType] = parsedResult.mealPlan[mealType].map((meal, index) => {
            console.log(`  Meal ${index + 1}:`, meal);

            // Check if meal is completely invalid
            if (!meal || typeof meal !== 'object') {
              console.warn(`❌ Invalid meal object in ${mealType}:`, meal);
              const fallbackMeal = availableMeals.find(m => m.name && m.name.toLowerCase().includes('adobo')) || availableMeals[0];
              return {
                mealName: fallbackMeal.name || 'Adobo',
                reason: 'Fallback meal selection'
              };
            }

            // Check if meal name is invalid
            if (!meal.mealName || meal.mealName === 'undefined' || meal.mealName === 'null' || meal.mealName.trim() === '') {
              console.warn(`❌ Invalid meal name in ${mealType}:`, meal);
              // Find a fallback meal from available meals
              const fallbackMeal = availableMeals.find(m => m.name && m.name.toLowerCase().includes('adobo')) || availableMeals[0];
              meal.mealName = fallbackMeal.name || 'Adobo';
              meal.reason = 'Fallback meal selection';
            }

            // Ensure all properties have valid values
            const validatedMeal = {
              mealName: meal.mealName && meal.mealName !== 'undefined' ? meal.mealName : 'Adobo',
              reason: meal.reason && meal.reason !== 'undefined' ? meal.reason : 'A delicious Filipino dish'
            };

            console.log(`  ✅ Validated meal:`, validatedMeal);
            return validatedMeal;
          });
        }
      }

      return parsedResult;
    } catch (error) {
      console.error('Error editing meal plan:', error);
      throw new Error(`Failed to edit meal plan: ${error.message}`);
    }
  }

  // Analyze meals for family compatibility
  async analyzeMealsForFamily(familyProfile, meals, mealType = null) {
    try {
      // Create detailed family profile summary
      const familySummary = this.createFamilyProfileSummary(familyProfile);

      // Optimized meal information for 12-meal batches
      const mealDetails = meals.map((meal, index) =>
        `${index + 1}."${meal.name}"-${meal.calories || 0}cal-${meal.ingredients?.slice(0, 2).join(',') || 'none'}-allergens:${meal.allergens?.join(',') || 'none'}-${meal.dietType || 'general'}`
      ).join(' | ');

      const prompt = `Check meals for family. Return ONLY this format:

Family: ${familySummary}
Meals: ${mealDetails}

Return exactly this JSON:
[
{"mealId":"1","member":"You","concern":"All is well"},
{"mealId":"1","member":"Taha","concern":"has milk"},
{"mealId":"2","member":"You","concern":"All is well"},
{"mealId":"2","member":"Taha","concern":"All is well"}
]

Rules: For each meal, check each family member. If OK say "All is well". If problem say short issue like "not halal", "has milk", "not vegetarian". Return array only.`;

      const response = await this.generateContent(prompt);
      console.log('🚀 12-meal batch AI response received');

      const cleanedResponse = this.extractJsonFromResponse(response);

      if (!cleanedResponse) {
        throw new Error('Empty AI response');
      }

      let ultraSimpleResult;
      try {
        ultraSimpleResult = JSON.parse(cleanedResponse);
      } catch (parseError) {
        console.log('🔧 JSON parse failed, creating smart fallback...');
        // Create smart fallback analysis based on meal data
        ultraSimpleResult = this.createUltraSimpleFallback(meals, familyProfile);
      }

      // Validate ultra-simple format (should be array)
      if (!Array.isArray(ultraSimpleResult)) {
        console.log('❌ Response not array, creating emergency fallback');
        ultraSimpleResult = this.createUltraSimpleFallback(meals, familyProfile);
      }

      // Group by meal ID and transform to expected format
      const mealGroups = {};
      ultraSimpleResult.forEach(item => {
        if (!mealGroups[item.mealId]) {
          mealGroups[item.mealId] = [];
        }
        mealGroups[item.mealId].push({
          memberName: item.member,
          compatible: item.concern === "All is well",
          compatibilityLevel: item.concern === "All is well" ? "good" : "poor",
          reasons: [item.concern],
          concerns: item.concern === "All is well" ? [] : [item.concern],
          benefits: item.concern === "All is well" ? ["Suitable"] : []
        });
      });

      // Transform to expected format
      const analysisResult = {
        mealAnalysis: meals.map((meal, index) => {
          const mealId = (index + 1).toString();
          const familyCompatibility = mealGroups[mealId] || [];
          const hasIssues = familyCompatibility.some(member => !member.compatible);

          return {
            mealName: meal.name,
            mealId: meal._id || meal.id || meal.name,
            overallCompatibility: hasIssues ? "poor" : "good",
            familyCompatibility: familyCompatibility,
            generalNotes: "AI analysis",
            suggestions: "Check ingredients"
          };
        })
      };

      console.log('✅ Ultra-simple analysis completed:', analysisResult.mealAnalysis.length, 'meals analyzed');
      return analysisResult;
    } catch (error) {
      console.error('❌ Error analyzing meals for family:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        familyProfile: JSON.stringify(familyProfile, null, 2),
        mealsCount: meals.length
      });

      // Return a fallback response with more helpful information
      return {
        mealAnalysis: meals.map(meal => ({
          mealName: meal.name,
          mealId: meal._id || meal.id || meal.name,
          overallCompatibility: 'unknown',
          familyCompatibility: [
            {
              memberName: 'You',
              compatible: true,
              compatibilityLevel: 'unknown',
              reasons: ['AI analysis temporarily unavailable. Please check ingredients manually.'],
              concerns: ['Unable to analyze dietary compatibility at this time'],
              benefits: ['Please review meal details for nutritional information']
            }
          ],
          generalNotes: `Analysis temporarily unavailable due to: ${error.message}`,
          suggestions: 'Please review ingredients and nutritional information manually, or try again later.'
        }))
      };
    }
  }

  // Ultra-compact family profile for speed
  createFamilyProfileSummary(familyProfile) {
    let summary = '';

    // Main user profile
    if (familyProfile.user) {
      const prefs = familyProfile.user.dietaryPreferences || {};
      summary += `You:${prefs.restrictions?.join(',') || 'none'},${prefs.allergies?.join(',') || 'none'} `;
    }

    // Family members
    if (familyProfile.familyMembers && familyProfile.familyMembers.length > 0) {
      familyProfile.familyMembers.forEach((member) => {
        const prefs = member.dietaryPreferences || {};
        summary += `${member.name}:${prefs.restrictions?.join(',') || 'none'},${prefs.allergies?.join(',') || 'none'} `;
      });
    }

    return summary.trim();
  }

  // Create smart fallback analysis based on basic dietary rules
  createSmartFallback(meals, familyProfile) {
    const allMembers = [
      { name: 'You', prefs: familyProfile.user?.dietaryPreferences || {} },
      ...(familyProfile.familyMembers || []).map(member => ({
        name: member.name,
        prefs: member.dietaryPreferences || {}
      }))
    ];

    return {
      meals: meals.map(meal => ({
        name: meal.name,
        id: meal._id || meal.id || meal.name,
        concerns: allMembers.map(member => {
          const restrictions = member.prefs.restrictions || [];
          const allergies = member.prefs.allergies || [];
          const mealIngredients = (meal.ingredients || []).join(' ').toLowerCase();
          const mealAllergens = meal.allergens || [];
          const mealDietType = (meal.dietType || '').toLowerCase();

          // Check allergies first
          for (const allergy of allergies) {
            if (mealAllergens.includes(allergy) || mealIngredients.includes(allergy.toLowerCase())) {
              return { member: member.name, issue: `has ${allergy}` };
            }
          }

          // Check dietary restrictions
          for (const restriction of restrictions) {
            const restrictionLower = restriction.toLowerCase();

            if (restrictionLower === 'vegetarian' &&
                (mealIngredients.includes('chicken') || mealIngredients.includes('beef') ||
                 mealIngredients.includes('pork') || mealIngredients.includes('fish'))) {
              return { member: member.name, issue: 'not vegetarian' };
            }

            if (restrictionLower === 'vegan' &&
                (mealIngredients.includes('chicken') || mealIngredients.includes('beef') ||
                 mealIngredients.includes('pork') || mealIngredients.includes('fish') ||
                 mealIngredients.includes('milk') || mealIngredients.includes('egg'))) {
              return { member: member.name, issue: 'not vegan' };
            }

            if (restrictionLower === 'halal' &&
                (mealIngredients.includes('pork') || mealIngredients.includes('alcohol'))) {
              return { member: member.name, issue: 'not halal' };
            }
          }

          return { member: member.name, issue: 'All is well' };
        })
      }))
    };
  }

  // Create ultra-simple fallback in the exact format AI should return
  createUltraSimpleFallback(meals, familyProfile) {
    const allMembers = [
      { name: 'You', prefs: familyProfile.user?.dietaryPreferences || {} },
      ...(familyProfile.familyMembers || []).map(member => ({
        name: member.name,
        prefs: member.dietaryPreferences || {}
      }))
    ];

    const result = [];

    meals.forEach((meal, mealIndex) => {
      const mealId = (mealIndex + 1).toString();

      allMembers.forEach(member => {
        const restrictions = member.prefs.restrictions || [];
        const allergies = member.prefs.allergies || [];
        const mealIngredients = (meal.ingredients || []).join(' ').toLowerCase();
        const mealAllergens = meal.allergens || [];

        // Check allergies first
        for (const allergy of allergies) {
          if (mealAllergens.includes(allergy) || mealIngredients.includes(allergy.toLowerCase())) {
            result.push({
              mealId: mealId,
              member: member.name,
              concern: `has ${allergy}`
            });
            return;
          }
        }

        // Check dietary restrictions
        for (const restriction of restrictions) {
          const restrictionLower = restriction.toLowerCase();

          if (restrictionLower === 'vegetarian' &&
              (mealIngredients.includes('chicken') || mealIngredients.includes('beef') ||
               mealIngredients.includes('pork') || mealIngredients.includes('fish'))) {
            result.push({
              mealId: mealId,
              member: member.name,
              concern: 'not vegetarian'
            });
            return;
          }

          if (restrictionLower === 'halal' &&
              (mealIngredients.includes('pork') || mealIngredients.includes('alcohol'))) {
            result.push({
              mealId: mealId,
              member: member.name,
              concern: 'not halal'
            });
            return;
          }
        }

        // If no issues found
        result.push({
          mealId: mealId,
          member: member.name,
          concern: 'All is well'
        });
      });
    });

    return result;
  }

  // Validate meal plan before saving - checks dietary restrictions, allergies, and calorie intake
  async validateMealPlan(userProfile, familyMembers, selectedMeals) {
    try {
      console.log('🔍 Starting meal plan validation...');

      // Prepare all family members including the user
      const allMembers = [
        {
          name: 'You',
          dietaryPreferences: userProfile.dietaryPreferences || {},
          calorieTarget: userProfile.dietaryPreferences?.calorieTarget || 2000
        },
        ...(familyMembers || []).map(member => ({
          name: member.name,
          dietaryPreferences: member.dietaryPreferences || {},
          calorieTarget: member.dietaryPreferences?.calorieTarget || 2000
        }))
      ];

      // Calculate total calories for the meal plan
      const totalCalories = selectedMeals.reduce((total, meal) => total + (meal.calories || 0), 0);

      // Prepare meal details for AI analysis
      const mealDetails = selectedMeals.map((meal, index) =>
        `${index + 1}. "${meal.name}" - ${meal.calories || 0} calories - Ingredients: ${(meal.ingredients || []).join(', ')} - Allergens: ${(meal.allergens || []).join(', ')} - Diet Type: ${meal.dietType || 'general'}`
      ).join('\n');

      // Prepare family profile summary
      const familyProfileSummary = allMembers.map(member => {
        const prefs = member.dietaryPreferences;
        return `${member.name}: Restrictions: ${(prefs.restrictions || []).join(', ') || 'none'}, Allergies: ${(prefs.allergies || []).join(', ') || 'none'}, Daily Calorie Target: ${member.calorieTarget}`;
      }).join('\n');

      const prompt = `Analyze this meal plan for dietary compatibility and calorie intake for each family member.

MEAL PLAN:
${mealDetails}

FAMILY PROFILE:
${familyProfileSummary}

TOTAL MEAL PLAN CALORIES: ${totalCalories}

For each family member, check:
1. Dietary restrictions (e.g., vegetarian can't have meat, halal can't have pork/alcohol, vegan can't have animal products)
2. Allergies (e.g., can't have meals containing their allergens)
3. Calorie intake (check if meal plan calories significantly exceed their daily target by more than 500 calories)

Return ONLY this JSON format:
[
  {
    "memberName": "member name",
    "concern": "specific concern or 'All is well'"
  }
]

Rules for better concern messages:
- For allergies: "This meal plan contains [specific allergen] in [meal name], which could be dangerous for [member name] who is allergic to [allergen]"
- For vegetarian violations: "This meal plan includes [meal name] which contains meat and is not suitable for [member name]'s vegetarian diet"
- For vegan violations: "This meal plan includes [meal name] which contains animal products and is not suitable for [member name]'s vegan lifestyle"
- For halal violations: "This meal plan includes [meal name] which contains pork/alcohol and violates [member name]'s halal dietary requirements"
- For calorie excess: "This meal plan totals ${totalCalories} calories, which significantly exceeds [member name]'s daily target of [target] calories and may lead to weight gain"
- For multiple issues: Focus on the most serious (allergies > dietary restrictions > calories)
- If no issues: "All is well"
- Be specific about meal names and health implications
- Keep messages under 100 characters but informative

Only return the JSON array, no additional text.`;

      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);

      let validationResult;
      try {
        validationResult = JSON.parse(cleanedResponse);
      } catch (parseError) {
        console.log('🔧 JSON parse failed for meal plan validation, creating fallback...');
        validationResult = this.createMealPlanValidationFallback(allMembers, selectedMeals, totalCalories);
      }

      // Ensure result is an array
      if (!Array.isArray(validationResult)) {
        console.log('❌ Validation result not array, creating fallback');
        validationResult = this.createMealPlanValidationFallback(allMembers, selectedMeals, totalCalories);
      }

      // Determine if there are any concerns
      const hasIssues = validationResult.some(member => member.concern !== 'All is well');

      console.log('✅ Meal plan validation completed:', { hasIssues, memberCount: validationResult.length });

      return {
        hasIssues,
        memberValidations: validationResult,
        totalCalories,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Error validating meal plan:', error);

      // Return fallback validation result
      const allMembers = [
        {
          name: 'You',
          dietaryPreferences: userProfile.dietaryPreferences || {},
          calorieTarget: userProfile.dietaryPreferences?.calorieTarget || 2000
        },
        ...(familyMembers || []).map(member => ({
          name: member.name,
          dietaryPreferences: member.dietaryPreferences || {},
          calorieTarget: member.dietaryPreferences?.calorieTarget || 2000
        }))
      ];

      const totalCalories = selectedMeals.reduce((total, meal) => total + (meal.calories || 0), 0);

      return {
        hasIssues: false,
        memberValidations: allMembers.map(member => ({
          memberName: member.name,
          concern: 'Validation temporarily unavailable - please review manually'
        })),
        totalCalories,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Create fallback validation when AI analysis fails
  createMealPlanValidationFallback(allMembers, selectedMeals, totalCalories) {
    return allMembers.map(member => {
      const prefs = member.dietaryPreferences;
      const restrictions = prefs.restrictions || [];
      const allergies = prefs.allergies || [];
      const calorieTarget = member.calorieTarget || 2000;

      // Check for allergen conflicts first (most serious)
      for (const meal of selectedMeals) {
        const mealAllergens = meal.allergens || [];
        const mealIngredients = (meal.ingredients || []).join(' ').toLowerCase();

        for (const allergy of allergies) {
          if (mealAllergens.includes(allergy) || mealIngredients.includes(allergy.toLowerCase())) {
            return {
              memberName: member.name,
              concern: `This meal plan contains ${allergy} in ${meal.name}, which could be dangerous for ${member.name} who is allergic to ${allergy}`
            };
          }
        }
      }

      // Check for dietary restriction conflicts
      for (const meal of selectedMeals) {
        const mealIngredients = (meal.ingredients || []).join(' ').toLowerCase();

        for (const restriction of restrictions) {
          const restrictionLower = restriction.toLowerCase();

          if (restrictionLower === 'vegetarian' &&
              (mealIngredients.includes('chicken') || mealIngredients.includes('beef') ||
               mealIngredients.includes('pork') || mealIngredients.includes('fish'))) {
            return {
              memberName: member.name,
              concern: `This meal plan includes ${meal.name} which contains meat and is not suitable for ${member.name}'s vegetarian diet`
            };
          }

          if (restrictionLower === 'vegan' &&
              (mealIngredients.includes('chicken') || mealIngredients.includes('beef') ||
               mealIngredients.includes('pork') || mealIngredients.includes('fish') ||
               mealIngredients.includes('milk') || mealIngredients.includes('egg') ||
               mealIngredients.includes('cheese') || mealIngredients.includes('butter'))) {
            return {
              memberName: member.name,
              concern: `This meal plan includes ${meal.name} which contains animal products and is not suitable for ${member.name}'s vegan lifestyle`
            };
          }

          if (restrictionLower === 'halal' &&
              (mealIngredients.includes('pork') || mealIngredients.includes('alcohol'))) {
            return {
              memberName: member.name,
              concern: `This meal plan includes ${meal.name} which contains pork/alcohol and violates ${member.name}'s halal dietary requirements`
            };
          }
        }
      }

      // Check calorie excess last (least serious)
      if (totalCalories > calorieTarget + 500) {
        return {
          memberName: member.name,
          concern: `This meal plan totals ${totalCalories} calories, which significantly exceeds ${member.name}'s daily target of ${calorieTarget} calories and may lead to weight gain`
        };
      }

      // No issues found
      return {
        memberName: member.name,
        concern: 'All is well'
      };
    });
  }

  // Helper function to calculate age from date of birth
  calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  }
}

module.exports = new GeminiService();
