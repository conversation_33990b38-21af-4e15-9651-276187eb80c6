# Testing Activity Logs Feature - COMPREHENSIVE GUIDE

## 🚀 IMMEDIATE TESTING STEPS

### 1. Start the Backend Server
```bash
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
npm start
```

### 2. Start the Frontend
```bash
cd "PanlasApp Website/panlasapp web"
npm start
```

### 3. **QUICK TEST - Create Test Activities**
1. Login as an admin user
2. Go to Admin Dashboard → "User Activity Logs" tab
3. Click the **🧪 Create Test Activities** button
4. You should see: "✅ Created 5 test activities successfully!"
5. The activity logs table should now show the test activities

### 4. **VERIFY DATABASE DIRECTLY** (Optional)
```bash
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
node scripts/testActivityLogging.js
```

### 3. Test Login Activity Logging
1. Go to http://localhost:3000/login
2. Login with any valid user credentials
3. Check the console logs for: `✅ Login activity logged successfully`

### 4. Test Activity Logs in Admin Dashboard
1. <PERSON><PERSON> as an admin user
2. Go to Admin Dashboard
3. Click on "User Activity Logs" tab
4. You should see the login activity you just performed

### 5. Test Logout Activity Logging
1. Click logout from the header dropdown
2. Check the network tab - you should see a POST request to `/api/users/logout`
3. Login again as admin and check the activity logs
4. You should see both login and logout activities

### 6. Test Profile Update Activity Logging
1. Go to your profile page
2. Update any profile information (name, date of birth, etc.)
3. Save the changes
4. Check the activity logs - you should see an "update_profile" activity

### 7. Test Meal Plan Activity Logging
1. Go to the meal planner
2. Add a meal to any date
3. Check the activity logs - you should see "create_meal_plan" or "update_meal_plan"
4. Delete a meal plan
5. Check the activity logs - you should see "delete_meal_plan"

## Expected Results

### Activity Log Table Should Show:
- **User**: Username and email
- **Action**: Color-coded badges (login=green, logout=red, etc.)
- **Description**: Human-readable description
- **IP Address**: User's IP address
- **Date & Time**: Formatted timestamp
- **Details**: Expandable JSON with additional info

### Filtering Should Work:
- **Action Type**: Filter by specific actions
- **User Search**: Search by username or email
- **Date Range**: Filter by date range
- **Pagination**: Navigate through pages

## Troubleshooting

### If Activities Are Not Showing:
1. Check browser console for errors
2. Check backend console for activity logging messages
3. Verify the user has admin permissions
4. Check if the Activity model exists in MongoDB
5. Verify the activity routes are working: `GET /api/activity/log`

### If Filtering Doesn't Work:
1. Check the network tab for API calls to `/api/activity/log`
2. Verify query parameters are being sent correctly
3. Check backend logs for any errors in the activity controller

### Common Issues:
1. **No activities showing**: User might not have admin permissions
2. **Login not logged**: Check if Activity model is imported in userController
3. **Logout not logged**: Verify logout endpoint is being called from frontend
4. **Profile updates not logged**: Check if middleware is applied to profile routes

## Manual Database Check

### Using MongoDB Compass or CLI:
```javascript
// Connect to your MongoDB database
use your_database_name

// Check if activities are being created
db.activities.find().sort({createdAt: -1}).limit(10)

// Check activities for a specific user
db.activities.find({user: ObjectId("USER_ID_HERE")})

// Check activities by action type
db.activities.find({action: "login"})
```

## API Testing with Postman/curl

### Get Activity Logs:
```bash
curl -X GET "http://localhost:5000/api/activity/log" \
  -H "x-auth-token: YOUR_ADMIN_TOKEN"
```

### Test with Filters:
```bash
curl -X GET "http://localhost:5000/api/activity/log?action=login&page=1&limit=10" \
  -H "x-auth-token: YOUR_ADMIN_TOKEN"
```

## Success Indicators

✅ **Login Activity**: Shows when users log in
✅ **Logout Activity**: Shows when users log out  
✅ **Profile Updates**: Shows when users update their profiles
✅ **Meal Plan Actions**: Shows create/update/delete meal plan activities
✅ **Filtering Works**: Can filter by action, user, and date range
✅ **Pagination Works**: Can navigate through multiple pages
✅ **Real-time Updates**: New activities appear when refreshing
✅ **Admin Only**: Only admin users can access activity logs
✅ **Responsive Design**: Works on mobile and desktop
✅ **Error Handling**: Graceful handling of API errors
