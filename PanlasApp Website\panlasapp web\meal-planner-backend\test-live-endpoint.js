const axios = require('axios');

async function testLiveEndpoint() {
  try {
    console.log('🧪 Testing live API endpoint...');

    // First, let's test if the server is responding
    console.log('\n1️⃣ Testing server health...');
    try {
      const healthResponse = await axios.get('http://localhost:5000/', {
        timeout: 5000
      });
      console.log('✅ Server is responding');
      console.log('Response status:', healthResponse.status);
    } catch (error) {
      console.log('❌ Server health check failed:', error.message);
      if (error.response) {
        console.log('Response status:', error.response.status);
        console.log('Response data:', error.response.data);
      }
      console.log('Full error:', error.code, error.errno);

      // Try the API endpoint anyway
      console.log('\n⚠️ Proceeding with API test anyway...');
    }

    // Test the lock endpoint without authentication (should get 401)
    console.log('\n2️⃣ Testing lock endpoint without auth (expecting 401)...');
    try {
      const response = await axios.put('http://localhost:5000/api/meal-plans/2025-08-14/lock', {
        isLocked: true
      });
      console.log('Unexpected success:', response.status, response.data);
    } catch (error) {
      if (error.response) {
        console.log(`Expected error - Status: ${error.response.status}`);
        console.log('Response:', error.response.data);
      } else {
        console.log('Network error:', error.message);
      }
    }

    // Test with a fake token (should get 401 or 403)
    console.log('\n3️⃣ Testing lock endpoint with fake auth...');
    try {
      const response = await axios.put('http://localhost:5000/api/meal-plans/2025-08-14/lock', {
        isLocked: true
      }, {
        headers: {
          'Authorization': 'Bearer fake-token'
        }
      });
      console.log('Unexpected success:', response.status, response.data);
    } catch (error) {
      if (error.response) {
        console.log(`Expected error - Status: ${error.response.status}`);
        console.log('Response:', error.response.data);
      } else {
        console.log('Network error:', error.message);
      }
    }

    console.log('\n✅ Test completed');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testLiveEndpoint();
