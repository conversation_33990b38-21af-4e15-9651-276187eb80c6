require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

async function listUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get all users
    const users = await User.find({}, 'email username firstName lastName isEmailVerified termsAccepted').limit(10);
    
    console.log(`Found ${users.length} users:`);
    users.forEach((user, index) => {
      console.log(`${index + 1}. Email: ${user.email}, Username: ${user.username}, Name: ${user.firstName} ${user.lastName}, Verified: ${user.isEmailVerified}, Terms: ${user.termsAccepted}`);
    });

    // Close connection
    await mongoose.connection.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

listUsers();
