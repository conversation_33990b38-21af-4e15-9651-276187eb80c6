const mongoose = require('mongoose');
const Activity = require('./models/Activity');
const User = require('./models/User');
const ActivityService = require('./services/activityService');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/meal-planner', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function createTestAdminActivities() {
  try {
    console.log('🔧 Creating test admin activities...');

    // Find or create an admin user
    let adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      adminUser = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: 'hashedpassword',
        isEmailVerified: true,
        isAdmin: true,
        firstName: 'Admin',
        lastName: 'User'
      });
      await adminUser.save();
      console.log('✅ Created admin user');
    }

    // Find or create a regular user
    let regularUser = await User.findOne({ email: '<EMAIL>' });
    if (!regularUser) {
      regularUser = new User({
        username: 'regularuser',
        email: '<EMAIL>',
        password: 'hashedpassword',
        isEmailVerified: true,
        firstName: 'Regular',
        lastName: 'User'
      });
      await regularUser.save();
      console.log('✅ Created regular user');
    }

    // Clear existing test activities
    await Activity.deleteMany({});
    console.log('🗑️ Cleared existing activities');

    // Create sample admin activities using ActivityService
    const adminActivities = [
      {
        action: 'login',
        details: { userAgent: 'Mozilla/5.0 Admin Browser' }
      },
      {
        action: 'disabled_account',
        details: { 
          targetUser: regularUser.email,
          targetUsername: regularUser.username,
          adminAction: true
        }
      },
      {
        action: 'made_admin',
        details: { 
          targetUser: '<EMAIL>',
          adminAction: true
        }
      },
      {
        action: 'deleted_feedback',
        details: { 
          feedbackSubject: 'Test Feedback',
          feedbackId: 'test123',
          adminAction: true
        }
      },
      {
        action: 'enabled_account',
        details: { 
          targetUser: regularUser.email,
          targetUsername: regularUser.username,
          adminAction: true
        }
      }
    ];

    // Create regular user activities
    const userActivities = [
      {
        action: 'login',
        details: { userAgent: 'Mozilla/5.0 User Browser' }
      },
      {
        action: 'submitted_feedback',
        details: { 
          feedbackSubject: 'App Performance Issue',
          feedbackCategory: 'bug_report'
        }
      },
      {
        action: 'create_meal_plan',
        details: { 
          date: '2025-01-15',
          mealType: 'breakfast'
        }
      }
    ];

    // Log admin activities
    console.log('📝 Creating admin activities...');
    for (const activityData of adminActivities) {
      const success = await ActivityService.logActivity(
        adminUser._id, 
        activityData.action, 
        activityData.details,
        { headers: { 'user-agent': 'Test Browser' }, ip: '***********' }
      );
      if (success) {
        console.log(`✅ Created admin activity: ${activityData.action}`);
      } else {
        console.log(`❌ Failed to create admin activity: ${activityData.action}`);
      }
    }

    // Log user activities
    console.log('📝 Creating user activities...');
    for (const activityData of userActivities) {
      const success = await ActivityService.logActivity(
        regularUser._id, 
        activityData.action, 
        activityData.details,
        { headers: { 'user-agent': 'Test Browser' }, ip: '***********' }
      );
      if (success) {
        console.log(`✅ Created user activity: ${activityData.action}`);
      } else {
        console.log(`❌ Failed to create user activity: ${activityData.action}`);
      }
    }

    // Test the recent activity retrieval
    console.log('\n🔍 Testing recent activity retrieval...');
    const activities = await Activity.find()
      .populate('user', 'username email firstName lastName')
      .sort({ createdAt: -1 })
      .limit(10);

    console.log(`📊 Retrieved ${activities.length} activities:`);
    activities.forEach((activity, index) => {
      console.log(`${index + 1}. ${activity.action} by ${activity.user ? activity.user.username : 'NO USER'} (${activity.user ? activity.user.email : 'NO EMAIL'}) at ${activity.createdAt}`);
    });

    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Error creating test activities:', error);
  } finally {
    mongoose.connection.close();
  }
}

createTestAdminActivities();
