require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

async function addTestDataToAllUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Test meal data with proper structure
    const testMeals = [
      {
        _id: '507f1f77bcf86cd799439011',
        name: '<PERSON><PERSON><PERSON> Manok',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Main Course'],
        dietaryTags: ['gluten-free'],
        rating: 4.5,
        calories: 350,
        protein: 25,
        carbs: 15,
        fat: 20,
        image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
        description: 'Classic Filipino chicken adobo with soy sauce and vinegar',
        ingredients: ['chicken', 'soy sauce', 'vinegar', 'garlic', 'bay leaves'],
        instructions: ['Marinate chicken', 'Cook in sauce', 'Simmer until tender'],
        addedAt: new Date(),
        addedToDate: '2025-08-25',
        addedToMealType: 'lunch'
      },
      {
        _id: '507f1f77bcf86cd799439012',
        name: 'Pancit Canton',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Noodles'],
        dietaryTags: ['vegetarian-friendly'],
        rating: 4.2,
        calories: 280,
        protein: 12,
        carbs: 45,
        fat: 8,
        image: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400',
        description: 'Delicious Filipino stir-fried noodles with vegetables',
        ingredients: ['canton noodles', 'vegetables', 'soy sauce', 'garlic'],
        instructions: ['Boil noodles', 'Stir-fry with vegetables', 'Season with sauce'],
        addedAt: new Date(),
        addedToDate: '2025-08-24',
        addedToMealType: 'dinner'
      },
      {
        _id: '507f1f77bcf86cd799439013',
        name: 'Sinigang na Baboy',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Soup'],
        dietaryTags: ['sour', 'comfort-food'],
        rating: 4.7,
        calories: 320,
        protein: 28,
        carbs: 12,
        fat: 18,
        image: 'https://images.unsplash.com/photo-**********-85f173990554?w=400',
        description: 'Traditional Filipino sour soup with pork and vegetables',
        ingredients: ['pork', 'tamarind', 'vegetables', 'tomatoes', 'onions'],
        instructions: ['Boil pork', 'Add tamarind', 'Add vegetables', 'Season to taste'],
        addedAt: new Date(),
        addedToDate: '2025-08-23',
        addedToMealType: 'lunch'
      },
      {
        _id: '507f1f77bcf86cd799439014',
        name: 'Lechon Kawali',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Pork'],
        dietaryTags: ['crispy', 'comfort-food'],
        rating: 4.6,
        calories: 420,
        protein: 30,
        carbs: 8,
        fat: 32,
        image: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400',
        description: 'Crispy Filipino pork belly with perfect crackling',
        ingredients: ['pork belly', 'salt', 'bay leaves', 'peppercorns'],
        instructions: ['Boil pork', 'Deep fry until crispy', 'Serve hot'],
        addedAt: new Date(),
        addedToDate: '2025-08-22',
        addedToMealType: 'dinner'
      },
      {
        _id: '507f1f77bcf86cd799439015',
        name: 'Chicken Tinola',
        mealType: ['lunch', 'dinner'],
        category: ['Filipino', 'Soup'],
        dietaryTags: ['healthy', 'comfort-food'],
        rating: 4.3,
        calories: 250,
        protein: 22,
        carbs: 18,
        fat: 12,
        image: 'https://images.unsplash.com/photo-**********-85f173990554?w=400',
        description: 'Comforting Filipino chicken soup with ginger and vegetables',
        ingredients: ['chicken', 'ginger', 'green papaya', 'malunggay', 'onions'],
        instructions: ['Sauté aromatics', 'Add chicken', 'Simmer with vegetables'],
        addedAt: new Date(),
        addedToDate: '2025-08-21',
        addedToMealType: 'lunch'
      }
    ];

    // Get all users
    const users = await User.find({});
    console.log(`Found ${users.length} users`);

    // Add test data to all users
    for (const user of users) {
      console.log(`Adding test data to user: ${user.email}`);
      
      // Add test meals to recently added meals
      user.recentlyAddedToMealPlans = testMeals;
      await user.save();
      
      console.log(`✓ Added ${testMeals.length} test meals to ${user.email}`);
    }

    // Close connection
    await mongoose.connection.close();
    console.log('\n✅ Test data added to ALL users successfully!');
    console.log('Now any logged-in user should see the meal data in the frontend.');
  } catch (error) {
    console.error('Error:', error);
  }
}

addTestDataToAllUsers();
