{"name": "panlas-mobile-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "expo build:android", "build:android-apk": "expo build:android -t apk"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "axios": "^1.6.2", "expo": "~53.0.9", "expo-linear-gradient": "~14.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}