const mongoose = require('mongoose');
const User = require('./models/User');

// Connect to MongoDB - Use the same connection as the server
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster';
mongoose.connect(MONGODB_URI);

async function checkUsersAndFamily() {
  try {
    console.log('🔍 Checking users and family members in database...');
    
    // Count total users
    const totalUsers = await User.countDocuments();
    console.log('👥 Total users in database:', totalUsers);
    
    if (totalUsers === 0) {
      console.log('❌ No users found in database');
      return;
    }
    
    // Find users with family members
    const usersWithFamily = await User.find({ 'familyMembers.0': { $exists: true } });
    console.log('👨‍👩‍👧‍👦 Users with family members:', usersWithFamily.length);
    
    // Get a sample user to check structure
    const sampleUser = await User.findOne().limit(1);
    if (sampleUser) {
      console.log('📋 Sample user structure:');
      console.log('  - Email:', sampleUser.email);
      console.log('  - Family members count:', sampleUser.familyMembers?.length || 0);
      
      if (sampleUser.familyMembers && sampleUser.familyMembers.length > 0) {
        const firstMember = sampleUser.familyMembers[0];
        console.log('  - First family member:');
        console.log('    - Name:', firstMember.name);
        console.log('    - Dietary preferences:', firstMember.dietaryPreferences);
        console.log('    - Disliked ingredients:', firstMember.dietaryPreferences?.dislikedIngredients);
      }
    }
    
    // Find the specific user that might be testing (look for recent activity)
    const recentUsers = await User.find().sort({ createdAt: -1 }).limit(5);
    console.log('\n🕒 Recent users:');
    recentUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} - Family members: ${user.familyMembers?.length || 0}`);
      if (user.familyMembers && user.familyMembers.length > 0) {
        user.familyMembers.forEach((member, memberIndex) => {
          console.log(`     - ${member.name}: disliked ingredients = ${member.dietaryPreferences?.dislikedIngredients || 'none'}`);
        });
      }
    });
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkUsersAndFamily();
