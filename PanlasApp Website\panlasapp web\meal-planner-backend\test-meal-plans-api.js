require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const mealPlanController = require('./controllers/mealPlanController');
const auth = require('./middleware/auth');

// Create a simple test server
const app = express();

app.use(cors());
app.use(express.json());

// Mock auth middleware for testing
const mockAuth = (req, res, next) => {
  req.user = { id: '685883ea3cc2df1d683b8714' }; // User ID from debug
  next();
};

// Test route
app.get('/meal-plans', mockAuth, mealPlanController.getMealPlans);

async function testMealPlansAPI() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const PORT = 5001; // Use different port to avoid conflicts
    const server = app.listen(PORT, () => {
      console.log(`🚀 Test server running on port ${PORT}`);
    });

    // Wait a moment for server to start
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test the API endpoint
    console.log('\n🧪 Testing Meal Plans API endpoint...');
    
    const axios = require('axios');
    
    try {
      const response = await axios.get(`http://localhost:${PORT}/meal-plans`);
      
      console.log('✅ API Response:');
      console.log('Status:', response.status);
      console.log('Number of meal plans:', response.data.length);
      
      // Check first few meal plans
      if (response.data.length > 0) {
        console.log('\n📋 First meal plan structure:');
        const firstPlan = response.data[0];
        console.log('Date:', firstPlan.date);
        console.log('Has meals array:', !!firstPlan.meals);
        console.log('Meals array length:', firstPlan.meals ? firstPlan.meals.length : 0);
        console.log('Has breakfast array:', !!firstPlan.breakfast);
        console.log('Breakfast array length:', firstPlan.breakfast ? firstPlan.breakfast.length : 0);
        console.log('Has lunch array:', !!firstPlan.lunch);
        console.log('Lunch array length:', firstPlan.lunch ? firstPlan.lunch.length : 0);
        console.log('Has dinner array:', !!firstPlan.dinner);
        console.log('Dinner array length:', firstPlan.dinner ? firstPlan.dinner.length : 0);
        console.log('Has snack array:', !!firstPlan.snack);
        console.log('Snack array length:', firstPlan.snack ? firstPlan.snack.length : 0);
        
        // Check meal data structure
        if (firstPlan.meals && firstPlan.meals.length > 0) {
          console.log('\n🍽️ First meal in meals array:');
          const firstMeal = firstPlan.meals[0];
          console.log('Meal Type:', firstMeal.mealType);
          console.log('Has meal object:', !!firstMeal.meal);
          if (firstMeal.meal) {
            console.log('Meal Name:', firstMeal.meal.name);
            console.log('Meal Image:', firstMeal.meal.image);
            console.log('Meal Calories:', firstMeal.meal.calories);
            console.log('Meal Description:', firstMeal.meal.description);
            console.log('Meal Category:', firstMeal.meal.category);
          }
        }
        
        // Check breakfast array data
        if (firstPlan.breakfast && firstPlan.breakfast.length > 0) {
          console.log('\n🥞 First breakfast meal:');
          const firstBreakfast = firstPlan.breakfast[0];
          console.log('Name:', firstBreakfast.name);
          console.log('Image:', firstBreakfast.image);
          console.log('Calories:', firstBreakfast.calories);
          console.log('Description:', firstBreakfast.description);
          console.log('Category:', firstBreakfast.category);
        }

        // Check lunch array data
        if (firstPlan.lunch && firstPlan.lunch.length > 0) {
          console.log('\n🍽️ First lunch meal:');
          const firstLunch = firstPlan.lunch[0];
          console.log('Name:', firstLunch.name);
          console.log('Image:', firstLunch.image);
          console.log('Calories:', firstLunch.calories);
          console.log('Description:', firstLunch.description);
          console.log('Category:', firstLunch.category);
          console.log('Full meal object:', JSON.stringify(firstLunch, null, 2));
        }

        // Check dinner array data
        if (firstPlan.dinner && firstPlan.dinner.length > 0) {
          console.log('\n🍖 First dinner meal:');
          const firstDinner = firstPlan.dinner[0];
          console.log('Name:', firstDinner.name);
          console.log('Image:', firstDinner.image);
          console.log('Calories:', firstDinner.calories);
          console.log('Description:', firstDinner.description);
          console.log('Category:', firstDinner.category);
        }
      }
      
    } catch (error) {
      if (error.response) {
        console.log('❌ API Error Response:');
        console.log('Status:', error.response.status);
        console.log('Data:', error.response.data);
      } else {
        console.log('❌ Network Error:', error.message);
      }
    }

    // Close server
    server.close();
    console.log('\n🔌 Test server closed');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

testMealPlansAPI();
