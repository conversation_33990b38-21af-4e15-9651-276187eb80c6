import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import TermsModal from '../TermsModal/TermsModal';
import termsService from '../../services/termsService';
import analyticsService from '../../services/analyticsService';
import { useAdminView } from '../../context/AdminViewContext';
import '../../../src/styles/Auth.css';

function Login() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState('');
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [loginResponse, setLoginResponse] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const { resetToUserViewOnLogin } = useAdminView();

  // Check if user is already logged in
  // useEffect(() => {
  //   const token = localStorage.getItem('token');
  //   if (token) {
  //     navigate('/home');
  //   }
  // }, [navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear API error when user makes changes
    if (apiError) {
      setApiError('');
    }

    // Real-time validation
    validateFieldRealTime(name, value);
  };

  const validateFieldRealTime = (fieldName, value) => {
    const newErrors = { ...errors };

    switch (fieldName) {
      case 'email':
        if (!value) {
          newErrors.email = 'Email is required';
        } else if (!/^[^\s@]+@gmail\.com$/i.test(value)) {
          newErrors.email = 'Email must be gmail';
        } else {
          delete newErrors.email;
        }
        break;

      case 'password':
        if (!value) {
          newErrors.password = 'Password is required';
        } else if (value.length < 5) {
          newErrors.password = 'Password must be at least 5 characters';
        } else {
          delete newErrors.password;
        }
        break;

      default:
        break;
    }

    setErrors(newErrors);
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Email validation - Gmail only
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@gmail\.com$/i.test(formData.email)) {
      newErrors.email = 'Email must be gmail';
    }
    
    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 5) {
      newErrors.password = 'Password must be at least 5 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setApiError('');
    
    try {
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.post(`${API_BASE_URL}/users/login`, {
        email: formData.email,
        password: formData.password
      });
      
      // Store the token immediately after successful login
      console.log('🔍 Login response:', response.data);
      console.log('🔍 Requires terms acceptance:', response.data.requiresTermsAcceptance);

      // Always store the token first
      localStorage.setItem('token', response.data.token);
      console.log('🔑 Token stored in localStorage');

      if (response.data.requiresTermsAcceptance) {
        console.log('📋 Showing terms modal');
        // Store login response and show terms modal
        setLoginResponse(response.data);
        setShowTermsModal(true);
      } else {
        console.log('✅ No terms required, completing login');
        // Complete login process (this will also store user data)
        completeLogin(response.data);
      }
    } catch (err) {
      setApiError(
        err.response?.data?.message || 
        'Login failed. Please check your credentials and try again.'
      );
      console.error('Login error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const completeLogin = (loginData) => {
    // Token is already stored, just store user info
    console.log('🏁 Completing login process');

    // Store user info (don't store sensitive data)
    if (loginData.user) {
      localStorage.setItem('user', JSON.stringify({
        id: loginData.user.id,
        email: loginData.user.email,
        username: loginData.user.username,
        isAdmin: loginData.user.isAdmin || false
      }));
      console.log('👤 User data stored in localStorage');

      // Log admin status for debugging
      if (loginData.user.isAdmin) {
        console.log('🔑 User has admin privileges - redirecting to user home first');
      }
    }

    // Enable analytics tracking after successful login
    analyticsService.enable();

    // Reset admin view to user view for all users (including admins)
    resetToUserViewOnLogin();

    // Always redirect to home page first (even for admins)
    // Admins can switch to admin view using the "View as Admin" button
    console.log('🏠 Redirecting to user home page');
    navigate("/home");
  };

  const handleTermsAccept = async () => {
    console.log('🔄 Terms accept button clicked');
    try {
      console.log('🔄 Calling termsService.acceptTerms()');
      const result = await termsService.acceptTerms();
      console.log('📝 Terms acceptance result:', result);

      if (result.success) {
        console.log('✅ Terms accepted successfully, completing login');
        setShowTermsModal(false);
        // Complete the login process
        completeLogin(loginResponse);
      } else {
        console.error('❌ Terms acceptance failed:', result.error);
        setApiError('Failed to accept terms: ' + result.error);
      }
    } catch (error) {
      console.error('💥 Error accepting terms:', error);
      setApiError('Failed to accept terms. Please try again.');
    }
  };

  const handleTermsDecline = () => {
    console.log('❌ User declined terms');
    // Clear stored data and show error
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setShowTermsModal(false);
    setLoginResponse(null);
    setApiError('You must accept the terms and conditions to use PanlasApp.');
  };

  return (
    <div className="auth-container">
      <div className="auth-box">
        <div className="heading">Welcome Back</div>
        
        {apiError && <div className="error-message">{apiError}</div>}
        
        <form className="form" onSubmit={handleSubmit}>
          <div className="input-group">
            <label htmlFor="email">Email Address</label>
            <input
              placeholder="Enter your email address"
              id="email"
              name="email"
              type="email"
              className={`input ${errors.email ? 'error' : ''}`}
              value={formData.email}
              onChange={handleChange}
            />
            {errors.email && <div className="validation-message">{errors.email}</div>}
          </div>

          <div className="input-group">
            <label htmlFor="password">Password</label>
            <div className="password-input-container">
              <input
                placeholder="Enter your password"
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                className={`input ${errors.password ? 'error' : ''}`}
                value={formData.password}
                onChange={handleChange}
              />
              <button
                type="button"
                className="password-toggle-btn"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  {showPassword ? (
                    // Eye-off icon (password hidden)
                    <>
                      <path
                        d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <line
                        x1="1"
                        y1="1"
                        x2="23"
                        y2="23"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </>
                  ) : (
                    // Eye icon (password visible)
                    <>
                      <path
                        d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <circle
                        cx="12"
                        cy="12"
                        r="3"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </>
                  )}
                </svg>
              </button>
            </div>
            {errors.password && <div className="validation-message">{errors.password}</div>}
          </div>
          
          <div className="forgot-password">
            <Link to="/forgot-password">Forgot Password?</Link>
          </div>
          
          <button 
            type="submit" 
            className="auth-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Signing In...' : 'Sign In'}
          </button>
        </form>
        
        <div className="auth-link">
          <span>Don't have an account? </span>
          <Link to="/signup">Sign Up</Link>
        </div>
      </div>

      {/* Terms and Conditions Modal */}
      <TermsModal
        isOpen={showTermsModal}
        onAccept={handleTermsAccept}
        onDecline={handleTermsDecline}
      />
    </div>
  );
}

export default Login;
