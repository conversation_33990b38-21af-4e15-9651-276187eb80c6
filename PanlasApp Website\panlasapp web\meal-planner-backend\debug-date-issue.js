require('dotenv').config();
const mongoose = require('mongoose');
const MealPlan = require('./models/MealPlan');

async function debugDateIssue() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find all meal plans for debugging
    console.log('\n📋 Finding all meal plans...');
    const allMealPlans = await MealPlan.find({}).limit(10);
    console.log(`Found ${allMealPlans.length} meal plans`);

    allMealPlans.forEach((plan, index) => {
      console.log(`\nMeal Plan ${index + 1}:`);
      console.log(`- ID: ${plan._id}`);
      console.log(`- User: ${plan.user}`);
      console.log(`- Date: "${plan.date}" (type: ${typeof plan.date})`);
      console.log(`- Created: ${plan.createdAt}`);
      console.log(`- Is Locked: ${plan.isLocked}`);
    });

    // Test specific date format for August 14, 2025
    const testDate = '2025-08-14';
    console.log(`\n🔍 Looking for meal plan with date: "${testDate}"`);
    
    const mealPlan = await MealPlan.findOne({ date: testDate });
    if (mealPlan) {
      console.log('✅ Found meal plan:');
      console.log(`- ID: ${mealPlan._id}`);
      console.log(`- User: ${mealPlan.user}`);
      console.log(`- Date: "${mealPlan.date}"`);
      console.log(`- Is Locked: ${mealPlan.isLocked}`);
      console.log(`- Meals count: ${mealPlan.meals ? mealPlan.meals.length : 0}`);

      // Check different meal storage formats
      console.log('- Meal plan structure:');
      if (mealPlan.meals && mealPlan.meals.length > 0) {
        console.log('  * meals array:', mealPlan.meals.length, 'items');
      }
      if (mealPlan.breakfast && mealPlan.breakfast.length > 0) {
        console.log('  * breakfast array:', mealPlan.breakfast.length, 'items');
      }
      if (mealPlan.lunch && mealPlan.lunch.length > 0) {
        console.log('  * lunch array:', mealPlan.lunch.length, 'items');
      }
      if (mealPlan.dinner && mealPlan.dinner.length > 0) {
        console.log('  * dinner array:', mealPlan.dinner.length, 'items');
      }
      if (mealPlan.snack && mealPlan.snack.length > 0) {
        console.log('  * snack array:', mealPlan.snack.length, 'items');
      }
    } else {
      console.log('❌ No meal plan found for this date');
    }

    // Test with Date object conversion
    console.log(`\n🔍 Testing with Date object conversion...`);
    const dateObj = new Date(testDate);
    console.log(`Date object: ${dateObj}`);
    console.log(`ISO string: ${dateObj.toISOString()}`);
    console.log(`Split result: ${dateObj.toISOString().split('T')[0]}`);

    const mealPlanWithDateObj = await MealPlan.findOne({ date: dateObj });
    if (mealPlanWithDateObj) {
      console.log('✅ Found meal plan with Date object');
    } else {
      console.log('❌ No meal plan found with Date object');
    }

    // Test different date formats that might exist
    const possibleFormats = [
      '2025-08-14',
      '2025-8-14',
      '08/14/2025',
      '14/08/2025',
      '2025/08/14',
      new Date('2025-08-14').toISOString(),
      new Date('2025-08-14').toString()
    ];

    console.log('\n🔍 Testing different date formats...');
    for (const format of possibleFormats) {
      const result = await MealPlan.findOne({ date: format });
      console.log(`Format "${format}": ${result ? '✅ Found' : '❌ Not found'}`);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

debugDateIssue();
