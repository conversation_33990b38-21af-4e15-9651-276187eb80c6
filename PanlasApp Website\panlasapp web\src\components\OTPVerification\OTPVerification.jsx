import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import analyticsService from '../../services/analyticsService';
import '../../styles/Auth.css';

const OTPVerification = () => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [resendCooldown, setResendCooldown] = useState(0);
  const inputRefs = useRef([]);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get email from navigation state
  const email = location.state?.email || '';

  useEffect(() => {
    // Redirect to signup if no email provided
    if (!email) {
      navigate('/signup');
      return;
    }

    // Focus first input on mount
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [email, navigate]);

  useEffect(() => {
    // Countdown timer for resend button
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleOtpChange = (value, index) => {
    if (value.length > 1) return; // Prevent multiple characters
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Clear error when user types
    if (error) setError('');

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e, index) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = async () => {
    const otpCode = otp.join('');
    
    if (otpCode.length !== 6) {
      setError('Please enter the complete 6-digit code');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.post(`${API_BASE_URL}/users/verify-otp`, {
        email: email,
        otp: otpCode
      });
      
      if (response.data.token) {
        // Store the JWT token in localStorage
        localStorage.setItem('token', response.data.token);

        // Store user info
        if (response.data.user) {
          localStorage.setItem('user', JSON.stringify({
            id: response.data.user.id,
            email: response.data.user.email,
            username: response.data.user.username
          }));
        }

        // Enable analytics tracking after successful verification
        analyticsService.enable();

        // Redirect to home page after successful verification
        navigate('/home');
      } else {
        setError(response.data.message || 'Invalid verification code');
        setOtp(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
      }
    } catch (err) {
      setError(
        err.response?.data?.message || 
        'Verification failed. Please try again.'
      );
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendCooldown > 0) return;

    setLoading(true);
    setError('');
    
    try {
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      await axios.post(`${API_BASE_URL}/users/resend-otp`, {
        email: email
      });
      
      setResendCooldown(60); // 60 seconds cooldown
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } catch (err) {
      setError(
        err.response?.data?.message || 
        'Failed to resend code. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBackToSignup = () => {
    navigate('/signup');
  };

  return (
    <div className="auth-container">
      <div className="auth-box">
        <button className="otp-back-button" onClick={handleBackToSignup}>
          ←
        </button>
        
        <div className="otp-container">
          <div className="otp-header">
            <div className="otp-icon">📧</div>
            <h2 className="otp-title">Enter Verification Code</h2>
            <p className="otp-subtitle">
              We've sent a 6-digit verification code to:
            </p>
            <p className="otp-email">{email}</p>
          </div>

          {error && <div className="error-message">{error}</div>}

          <div className="otp-input-container">
            {otp.map((digit, index) => (
              <input
                key={index}
                ref={(ref) => (inputRefs.current[index] = ref)}
                className={`otp-input ${digit ? 'filled' : ''}`}
                value={digit}
                onChange={(e) => handleOtpChange(e.target.value, index)}
                onKeyDown={(e) => handleKeyPress(e, index)}
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={1}
                disabled={loading}
              />
            ))}
          </div>

          <button
            className="otp-verify-button"
            onClick={handleVerifyOTP}
            disabled={loading || otp.join('').length !== 6}
          >
            {loading ? 'Verifying...' : 'Verify & Continue'}
          </button>

          <div className="otp-resend-container">
            <p className="otp-resend-text">Didn't receive the code?</p>
            <button
              className="otp-resend-button"
              onClick={handleResendOTP}
              disabled={loading || resendCooldown > 0}
            >
              {resendCooldown > 0 
                ? `Resend in ${resendCooldown}s` 
                : 'Resend Code'
              }
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OTPVerification;
