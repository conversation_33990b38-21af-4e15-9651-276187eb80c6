import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { useResponsiveScreen, getResponsiveDimensions, getResponsiveFontSize } from '../utils/responsive';

// Import all screens
import HomeScreen from '../screens/main/HomeScreen';
import MealPlansScreen from '../screens/main/MealPlansScreen';
import FavoritesScreen from '../screens/main/FavoritesScreen';
import ProfileScreen from '../screens/main/ProfileScreen';
import MealDetailScreen from '../screens/main/MealDetailScreen';
import CreateMealPlanScreen from '../screens/main/CreateMealPlanScreen';

import FamilyScreen from '../screens/main/FamilyScreen';
import HistoryScreen from '../screens/main/HistoryScreen';
import FeedbackScreen from '../screens/main/FeedbackScreen';
import HelpScreen from '../screens/main/HelpScreen';
import ChatScreen from '../screens/main/ChatScreen';
import AIChatScreen from '../screens/main/AIChatScreen';
import MealFilterScreen from '../screens/main/MealFilterScreen';
import DietaryPreferencesScreen from '../screens/main/DietaryPreferencesScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Home Stack Navigator
const HomeStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="HomeMain" component={HomeScreen} />
      <Stack.Screen name="MealDetail" component={MealDetailScreen} />
      <Stack.Screen name="Chat" component={ChatScreen} />
      <Stack.Screen name="AIChat" component={AIChatScreen} />
      <Stack.Screen name="MealFilter" component={MealFilterScreen} />
      <Stack.Screen name="DietaryPreferences" component={DietaryPreferencesScreen} />
    </Stack.Navigator>
  );
};

// Meal Plans Stack Navigator
const MealPlansStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MealPlansMain" component={MealPlansScreen} />
      <Stack.Screen name="CreateMealPlan" component={CreateMealPlanScreen} />
      <Stack.Screen name="MealDetail" component={MealDetailScreen} />
      <Stack.Screen name="AIChat" component={AIChatScreen} />
    </Stack.Navigator>
  );
};

// History Stack Navigator
const HistoryStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="HistoryMain" component={HistoryScreen} />
      <Stack.Screen name="MealDetail" component={MealDetailScreen} />
      <Stack.Screen name="AIChat" component={AIChatScreen} />
    </Stack.Navigator>
  );
};

// Profile Stack Navigator
const ProfileStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="ProfileMain" component={ProfileScreen} />
      <Stack.Screen name="Family" component={FamilyScreen} />
      <Stack.Screen name="Feedback" component={FeedbackScreen} />
      <Stack.Screen name="Help" component={HelpScreen} />
      <Stack.Screen name="AIChat" component={AIChatScreen} />
    </Stack.Navigator>
  );
};

const MainNavigator = () => {
  const { size: screenSize } = useResponsiveScreen();
  const dimensions = getResponsiveDimensions(screenSize);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'MealPlans') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'History') {
            iconName = focused ? 'time' : 'time-outline';
          } else if (route.name === 'Favorites') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={dimensions.iconSize} color={color} />;
        },
        tabBarActiveTintColor: '#20C5AF',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
        tabBarLabelStyle: {
          fontSize: getResponsiveFontSize('small', screenSize),
          fontWeight: '500',
        },
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
          paddingBottom: screenSize === 'small' ? 3 : 5,
          paddingTop: screenSize === 'small' ? 3 : 5,
          height: dimensions.tabBarHeight,
          minHeight: dimensions.tabBarHeight,
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeStack}
        options={{ tabBarLabel: 'Home' }}
      />
      <Tab.Screen
        name="MealPlans"
        component={MealPlansStack}
        options={{ tabBarLabel: 'Meal Plans' }}
      />
      <Tab.Screen
        name="History"
        component={HistoryStack}
        options={{ tabBarLabel: 'History' }}
      />
      <Tab.Screen
        name="Favorites"
        component={FavoritesScreen}
        options={{ tabBarLabel: 'Favorites' }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileStack}
        options={{ tabBarLabel: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

export default MainNavigator;
