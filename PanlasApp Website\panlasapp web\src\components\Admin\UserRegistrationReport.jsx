import React, { useState } from 'react';
import axios from 'axios';
import { saveAs } from 'file-saver';
import './UserRegistrationReport.css';

function UserRegistrationReport() {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [reportData, setReportData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const generateReport = async () => {
    if (!startDate || !endDate) {
      setError('Please select both start and end dates');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const token = localStorage.getItem('token');
      
      const config = {
        headers: {
          'x-auth-token': token
        },
        params: {
          startDate,
          endDate
        }
      };

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/admin/reports/registrations`, config);
      setReportData(response.data);
      setLoading(false);
    } catch (err) {
      setError('Failed to generate report: ' + (err.response?.data?.message || err.message));
      console.error('Report generation error:', err);
      setLoading(false);
    }
  };

  const exportToCSV = () => {
    if (!reportData || !reportData.users || reportData.users.length === 0) {
      setError('No data to export');
      return;
    }

    // Create CSV content
    const headers = ['Username', 'Email', 'Registration Date', 'Gender', 'Barangay'];
    
    const csvRows = [
      headers.join(','), // Header row
      ...reportData.users.map(user => [
        user.username,
        user.email,
        new Date(user.createdAt).toLocaleDateString(),
        user.gender || 'Not specified',
        user.barangay || 'Not specified'
      ].join(','))
    ];
    
    const csvContent = csvRows.join('\n');
    
    // Create file and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const fileName = `user_registrations_${startDate}_to_${endDate}.csv`;
    saveAs(blob, fileName);
  };

  return (
    <div className="user-registration-report">
      <h2>User Registration Report</h2>
      
      <div className="report-controls">
        <div className="date-range">
          <div className="date-input">
            <label htmlFor="start-date">Start Date:</label>
            <input
              type="date"
              id="start-date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
            />
          </div>
          
          <div className="date-input">
            <label htmlFor="end-date">End Date:</label>
            <input
              type="date"
              id="end-date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
            />
          </div>
        </div>
        
        <button 
          className="generate-btn"
          onClick={generateReport}
          disabled={loading || !startDate || !endDate}
        >
          {loading ? 'Generating...' : 'Generate Report'}
        </button>
      </div>
      
      {error && <div className="report-error">{error}</div>}
      
      {reportData && (
        <div className="report-results">
          <div className="report-summary">
            <h3>Report Summary</h3>
            <p>
              <strong>Date Range:</strong> {new Date(reportData.dateRange.start).toLocaleDateString()} to {new Date(reportData.dateRange.end).toLocaleDateString()}
            </p>
            <p>
              <strong>Total Registrations:</strong> {reportData.totalCount}
            </p>
            
            <button 
              className="export-btn"
              onClick={exportToCSV}
              disabled={reportData.totalCount === 0}
            >
              Export to CSV
            </button>
          </div>
          
          {reportData.totalCount > 0 ? (
            <table className="report-table">
              <thead>
                <tr>
                  <th>Username</th>
                  <th>Email</th>
                  <th>Registration Date</th>
                  <th>Gender</th>
                  <th>Barangay</th>
                </tr>
              </thead>
              <tbody>
                {reportData.users.map((user, index) => (
                  <tr key={index}>
                    <td>{user.username}</td>
                    <td>{user.email}</td>
                    <td>{new Date(user.createdAt).toLocaleString()}</td>
                    <td>{user.gender || 'Not specified'}</td>
                    <td>{user.barangay || 'Not specified'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="no-data">No registrations found in the selected date range</div>
          )}
        </div>
      )}
    </div>
  );
}

export default UserRegistrationReport;
