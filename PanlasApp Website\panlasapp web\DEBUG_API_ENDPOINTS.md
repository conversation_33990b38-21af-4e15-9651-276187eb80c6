# 🔧 API ENDPOINTS DEBUGGING GUIDE

## 🚨 IMMEDIATE TESTING STEPS

### 1. **Start Backend Server**
```bash
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
npm start
```

**Look for these console messages:**
```
✅ Loading activity routes...
✅ Activity routes loaded at /api/activity
```

### 2. **Test Basic Connectivity**

#### Test 1: Basic Server Health
```bash
curl http://localhost:5000/
```
**Expected:** `Filipino Meal Planner API is running`

#### Test 2: CORS Test
```bash
curl http://localhost:5000/api/cors-test
```
**Expected:** JSON response with CORS info

#### Test 3: Activity Debug Test
```bash
curl http://localhost:5000/api/debug/activity-test
```
**Expected:** JSON with available activity endpoints

### 3. **Test Activity Endpoints (No Auth Required)**

#### Test 4: Activity Ping
```bash
curl http://localhost:5000/api/activity/ping
```
**Expected:** `{"message": "Activity routes are working!", ...}`

#### Test 5: Simple Activity Test
```bash
curl -X POST http://localhost:5000/api/activity/test-simple \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```
**Expected:** JSON response with test data

### 4. **Test Activity Endpoints (Auth Required)**

#### Get Your Auth Token:
1. Login to the frontend
2. Open browser console
3. Run: `localStorage.getItem('token')`
4. Copy the token value

#### Test 6: Database Status (Replace YOUR_TOKEN)
```bash
curl http://localhost:5000/api/activity/status \
  -H "x-auth-token: YOUR_TOKEN"
```

#### Test 7: Create Test Activities (Replace YOUR_TOKEN)
```bash
curl -X POST http://localhost:5000/api/activity/test \
  -H "x-auth-token: YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

#### Test 8: Get Activity Logs (Replace YOUR_TOKEN)
```bash
curl http://localhost:5000/api/activity/log \
  -H "x-auth-token: YOUR_TOKEN"
```

## 🎯 **FRONTEND TESTING**

### 1. **Open Frontend**
```bash
cd "PanlasApp Website/panlasapp web"
npm start
```

### 2. **Login as Admin**
- Go to http://localhost:3000/login
- Login with admin credentials

### 3. **Test Activity Logs**
- Go to Admin Dashboard → "User Activity Logs"
- Click **🔧 Test APIs** button
- Check browser console for results
- Click **🧪 Create Test Activities** button
- Click **🔄 Refresh Logs** button

## 🔍 **TROUBLESHOOTING**

### If Getting 404 Errors:

#### Check 1: Backend Console
Look for these messages when server starts:
```
✅ Loading activity routes...
✅ Activity routes loaded at /api/activity
```

#### Check 2: Route File Exists
```bash
ls "PanlasApp Website/panlasapp web/meal-planner-backend/routes/activityRoutes.js"
```

#### Check 3: Controller File Exists
```bash
ls "PanlasApp Website/panlasapp web/meal-planner-backend/controllers/activityController.js"
```

#### Check 4: Test Basic Endpoints First
- Test `/api/activity/ping` (no auth)
- Test `/api/activity/test-simple` (no auth)
- Then test auth endpoints

### If Getting 401 Errors:

#### Check 1: Token Exists
```javascript
// In browser console
console.log('Token:', localStorage.getItem('token'));
```

#### Check 2: Token Format
Token should start with `eyJ...`

#### Check 3: User is Admin
```javascript
// In browser console
const user = JSON.parse(localStorage.getItem('user'));
console.log('Is Admin:', user?.isAdmin);
```

### If Getting 500 Errors:

#### Check 1: Backend Console
Look for error messages in the backend console

#### Check 2: Database Connection
```bash
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
node scripts/testActivityLogging.js
```

#### Check 3: Environment Variables
Make sure `.env` file has:
```
JWT_SECRET=your_jwt_secret
MONGODB_URI=your_mongodb_connection_string
```

## 📊 **EXPECTED RESULTS**

### ✅ **Success Indicators:**
1. **Ping Test:** Returns activity routes working message
2. **Simple Test:** Returns test data with timestamp
3. **Auth Test:** Returns database status with activity counts
4. **Create Test:** Returns "Created X test activities successfully"
5. **Activity Logs:** Shows activities in the admin dashboard table

### ❌ **Failure Indicators:**
1. **404 Error:** Routes not loaded or incorrect URL
2. **401 Error:** Authentication token missing or invalid
3. **403 Error:** User not admin or insufficient permissions
4. **500 Error:** Server error, check backend console

## 🚀 **QUICK FIX COMMANDS**

### Restart Everything:
```bash
# Terminal 1 - Backend
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
npm start

# Terminal 2 - Frontend
cd "PanlasApp Website/panlasapp web"
npm start
```

### Clear Browser Data:
```javascript
// In browser console
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### Test Database Directly:
```bash
cd "PanlasApp Website/panlasapp web/meal-planner-backend"
node scripts/testActivityLogging.js
```

## 📞 **SUPPORT ENDPOINTS**

- **Health Check:** `GET /`
- **CORS Test:** `GET /api/cors-test`
- **Activity Debug:** `GET /api/debug/activity-test`
- **Activity Ping:** `GET /api/activity/ping`
- **Simple Test:** `POST /api/activity/test-simple`

All these endpoints should work without authentication and help identify where the issue is occurring.
