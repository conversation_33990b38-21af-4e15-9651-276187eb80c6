import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import apiService from '../../../meal-planner-backend/services/apiService';
import { useAdminView } from '../../context/AdminViewContext';
import axios from 'axios';

const AdminRoute = ({ children }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [adminRole, setAdminRole] = useState(null);
  const [adminPermissions, setAdminPermissions] = useState([]);
  const { isViewingAsUser } = useAdminView();

  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        // First check if user is authenticated at all
        if (!apiService.isAuthenticated()) {
          setIsLoading(false);
          return;
        }

        // Get user profile to check admin status
        const userProfile = await apiService.getUserProfile();
        const isUserAdmin = userProfile.isAdmin === true;
        setIsAdmin(isUserAdmin);

        // If user is admin, get their admin role and permissions
        if (isUserAdmin) {
          try {
            const token = localStorage.getItem('token');
            const config = {
              headers: {
                'x-auth-token': token
              }
            };

            const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
            const adminRes = await axios.get(`${API_BASE_URL}/admin/users/${userProfile._id}/admin-info`, config);
            setAdminRole(adminRes.data.role || 'admin');
            setAdminPermissions(adminRes.data.permissions || []);
          } catch (adminError) {
            // If admin info not found, default to basic admin
            setAdminRole('admin');
            setAdminPermissions(['user_management', 'analytics_view', 'system_health']);
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsLoading(false);
      }
    };

    checkAdminStatus();
  }, []);

  // Show loading state while checking admin status
  if (isLoading) {
    return <div>Checking permissions...</div>;
  }

  // If not authenticated or not an admin (including sub admin), redirect to home
  if (!apiService.isAuthenticated() || !isAdmin) {
    console.log('User not authorized to access admin area');
    return <Navigate to="/home" replace />;
  }

  // If admin is viewing as user, redirect to home
  if (isViewingAsUser) {
    console.log('Admin is viewing as user, redirecting to home');
    return <Navigate to="/home" replace />;
  }

  // If admin (including sub admin) and not viewing as user, render the protected component
  return children;
};

export default AdminRoute;
