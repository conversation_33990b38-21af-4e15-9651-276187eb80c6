<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Meal Plan Save</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug Meal Plan Save Issue</h1>
        
        <div class="section">
            <h3>1. Test Backend Connection</h3>
            <button class="button" onclick="testConnection()">Test Connection</button>
            <div id="connectionResult"></div>
        </div>

        <div class="section">
            <h3>2. Test Authentication</h3>
            <input type="text" id="authToken" placeholder="Enter your auth token here">
            <button class="button" onclick="testAuth()">Test Auth</button>
            <div id="authResult"></div>
        </div>

        <div class="section">
            <h3>3. Test Meal Plan Save</h3>
            <input type="text" id="planName" placeholder="Meal Plan Name" value="Debug Test Plan">
            <button class="button" onclick="testMealPlanSave()">Test Save</button>
            <div id="saveResult"></div>
        </div>

        <div class="section">
            <h3>4. Current localStorage Data</h3>
            <button class="button" onclick="showLocalStorage()">Show localStorage</button>
            <div id="localStorageResult"></div>
        </div>

        <div class="section">
            <h3>5. Test Payload</h3>
            <pre id="testPayload"></pre>
        </div>
    </div>

    <script>
        // Test payload
        const testPayload = {
            name: "Debug Test Plan",
            startDate: "2025-08-01",
            endDate: "2025-08-03",
            budgetPerDay: 450,
            totalBudgetUsed: 1350,
            riceBowlsPerDay: {
                "2025-08-01": 2,
                "2025-08-02": 1,
                "2025-08-03": 2
            },
            mealTimes: {
                breakfast: "08:00",
                lunch: "12:00",
                dinner: "18:00",
                snack: "15:00"
            },
            meals: [
                {
                    date: "2025-08-01",
                    mealType: "breakfast",
                    mealData: {
                        _id: "test-meal-1",
                        name: "Test Breakfast",
                        calories: 300,
                        category: ["Breakfast"],
                        image: "",
                        description: "Test breakfast meal",
                        rating: 4,
                        protein: 15,
                        carbs: 45,
                        fat: 10,
                        price: 150,
                        dietaryTags: [],
                        ingredients: ["eggs", "bread"],
                        instructions: ["Cook eggs", "Toast bread"],
                        instanceId: "test-breakfast-1"
                    }
                }
            ]
        };

        document.getElementById('testPayload').textContent = JSON.stringify(testPayload, null, 2);

        async function testConnection() {
            const result = document.getElementById('connectionResult');
            result.innerHTML = '<div class="info">Testing connection...</div>';
            
            try {
                const response = await fetch('http://localhost:5000/api/health');
                if (response.ok) {
                    result.innerHTML = '<div class="success">✅ Backend is running!</div>';
                } else {
                    result.innerHTML = '<div class="error">❌ Backend responded with status: ' + response.status + '</div>';
                }
            } catch (error) {
                result.innerHTML = '<div class="error">❌ Connection failed: ' + error.message + '</div>';
            }
        }

        async function testAuth() {
            const token = document.getElementById('authToken').value;
            const result = document.getElementById('authResult');
            
            if (!token) {
                result.innerHTML = '<div class="error">Please enter an auth token</div>';
                return;
            }
            
            result.innerHTML = '<div class="info">Testing authentication...</div>';
            
            try {
                const response = await fetch('http://localhost:5000/api/meal-plans/save', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testPayload)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = '<div class="success">✅ Authentication successful! Response: <pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                } else {
                    result.innerHTML = '<div class="error">❌ Auth failed (' + response.status + '): <pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                }
            } catch (error) {
                result.innerHTML = '<div class="error">❌ Request failed: ' + error.message + '</div>';
            }
        }

        async function testMealPlanSave() {
            const planName = document.getElementById('planName').value;
            const token = document.getElementById('authToken').value;
            const result = document.getElementById('saveResult');
            
            if (!token) {
                result.innerHTML = '<div class="error">Please enter an auth token first</div>';
                return;
            }
            
            const payload = { ...testPayload, name: planName };
            result.innerHTML = '<div class="info">Testing meal plan save...</div>';
            
            try {
                const response = await fetch('http://localhost:5000/api/meal-plans/save', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = '<div class="success">✅ Meal plan saved successfully! Response: <pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                } else {
                    result.innerHTML = '<div class="error">❌ Save failed (' + response.status + '): <pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                }
            } catch (error) {
                result.innerHTML = '<div class="error">❌ Request failed: ' + error.message + '</div>';
            }
        }

        function showLocalStorage() {
            const result = document.getElementById('localStorageResult');
            const data = {
                token: localStorage.getItem('token'),
                mealPlan: localStorage.getItem('mealPlan'),
                lockedDates: localStorage.getItem('lockedDates'),
                mealTimes: localStorage.getItem('mealTimes')
            };
            
            result.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
    </script>
</body>
</html>
