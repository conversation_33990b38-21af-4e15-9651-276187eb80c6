const express = require('express');
const router = express.Router();
const geminiService = require('../services/geminiService');
const Meal = require('../models/Meal');
const User = require('../models/User');
const auth = require('../middleware/auth');

// Detect dietary conflicts
router.post('/dietary-conflicts', auth, async (req, res) => {
  try {
    // Handle both old format (direct preferences) and new format (with user and family)
    let requestData;

    if (req.body.userPreferences) {
      // New format with user and family preferences
      requestData = {
        userPreferences: req.body.userPreferences,
        familyMembers: req.body.familyMembers || []
      };
    } else {
      // Old format - direct preferences (for backward compatibility)
      const { restrictions, allergies, dislikedIngredients } = req.body;
      requestData = {
        restrictions,
        allergies,
        dislikedIngredients
      };
    }

    console.log('Dietary conflicts request data:', requestData);

    const conflicts = await geminiService.detectDietaryConflicts(requestData);

    res.json({
      success: true,
      conflicts
    });
  } catch (error) {
    console.error('Error detecting dietary conflicts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect dietary conflicts',
      error: error.message
    });
  }
});

// Generate personalized meal recommendations
router.post('/meal-recommendations', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { goalType, limit = 10 } = req.body;

    // Get user profile with family members and name
    const user = await User.findById(userId).select('firstName lastName dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all available meals from database
    const meals = await Meal.find(); // Get all meals for better recommendations

    // Generate AI recommendations
    const recommendations = await geminiService.generateMealRecommendations(
      {
        userName: user.firstName || 'User',
        dietaryPreferences: user.dietaryPreferences,
        familyMembers: user.familyMembers
      },
      meals,
      goalType
    );

    // Map AI recommendations to actual meal objects from database
    const recommendedMeals = [];
    for (const rec of recommendations.recommendations) {
      const meal = meals.find(m =>
        m.name.toLowerCase() === rec.mealName.toLowerCase()
      );
      if (meal && recommendedMeals.length < limit) {
        recommendedMeals.push({
          ...meal.toObject(),
          aiReason: rec.reason,
          nutritionalBenefits: rec.nutritionalBenefits,
          suitability: rec.suitability
        });
      }
    }

    res.json({
      success: true,
      recommendations: recommendedMeals,
      generalAdvice: recommendations.generalAdvice,
      totalFound: recommendedMeals.length
    });
  } catch (error) {
    console.error('Error generating meal recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate meal recommendations',
      error: error.message
    });
  }
});

// Generate goal-based dietary suggestions
router.post('/goal-suggestions', auth, async (req, res) => {
  try {
    const { goal, healthCondition } = req.body;

    if (!goal) {
      return res.status(400).json({
        success: false,
        message: 'Goal is required'
      });
    }

    const suggestions = await geminiService.generateGoalBasedSuggestions(goal, healthCondition);

    res.json({
      success: true,
      suggestions
    });
  } catch (error) {
    console.error('Error generating goal suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate goal suggestions',
      error: error.message
    });
  }
});

// Generate AI meal plan
router.post('/generate-meal-plan', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user profile with family members and name
    const user = await User.findById(userId).select('firstName lastName dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all available meals from database
    const meals = await Meal.find();

    // Generate AI meal plan
    const mealPlanResult = await geminiService.generateAIMealPlan(
      {
        userName: user.firstName || 'User',
        dietaryPreferences: user.dietaryPreferences,
        familyMembers: user.familyMembers
      },
      meals
    );

    res.json({
      success: true,
      mealPlan: mealPlanResult.mealPlan,
      nutritionalSummary: mealPlanResult.nutritionalSummary,
      personalizedMessage: mealPlanResult.personalizedMessage,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating AI meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate AI meal plan',
      error: error.message
    });
  }
});

// Generate AI weekly meal plan with diversity
router.post('/generate-weekly-meal-plan', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user profile with family members and name
    const user = await User.findById(userId).select('firstName lastName dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all available meals from database
    const meals = await Meal.find();

    console.log(`Generating weekly meal plan for user: ${user.firstName}, Available meals: ${meals.length}`);

    // Generate weekly AI meal plan with diversity
    const weeklyMealPlanResult = await geminiService.generateWeeklyAIMealPlan(
      {
        userName: user.firstName || 'User',
        dietaryPreferences: user.dietaryPreferences,
        familyMembers: user.familyMembers
      },
      meals
    );

    res.json({
      success: true,
      weeklyMealPlan: weeklyMealPlanResult.weeklyMealPlan,
      nutritionalSummary: weeklyMealPlanResult.nutritionalSummary,
      personalizedMessage: weeklyMealPlanResult.personalizedMessage,
      diversityAnalysis: weeklyMealPlanResult.diversityAnalysis,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating weekly AI meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate weekly AI meal plan',
      error: error.message
    });
  }
});


// Edit meal plan
router.post('/edit-meal-plan', auth, async (req, res) => {
  try {
    const { currentMealPlan, editRequest, isFamily = false } = req.body;

    if (!currentMealPlan || !editRequest) {
      return res.status(400).json({
        success: false,
        message: 'Current meal plan and edit request are required'
      });
    }

    // Get user profile with family members and name if needed
    const user = await User.findById(req.user.id).select('firstName lastName dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const userProfile = {
      userName: user.firstName || 'User',
      dietaryPreferences: user.dietaryPreferences || {
        restrictions: [],
        allergies: [],
        dislikedIngredients: [],
        calorieTarget: 2000,
        mealFrequency: 3
      },
      familyMembers: isFamily ? (user.familyMembers || []) : []
    };

    // Get all available meals from database
    const meals = await Meal.find();

    // Generate edited meal plan
    const editedMealPlan = await geminiService.editMealPlan(
      userProfile,
      meals,
      currentMealPlan,
      editRequest
    );

    res.json({
      success: true,
      mealPlan: editedMealPlan.mealPlan,
      nutritionalSummary: editedMealPlan.nutritionalSummary,
      personalizedMessage: editedMealPlan.personalizedMessage,
      conflicts: editedMealPlan.conflicts || [],
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error editing meal plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to edit meal plan',
      error: error.message
    });
  }
});

// Chat endpoint for general AI assistance
router.post('/chat', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { message, includeProfile = false, includeMeals = true } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Message is required'
      });
    }

    let context = {};

    if (includeProfile) {
      const user = await User.findById(userId).select('firstName lastName dietaryPreferences familyMembers');
      if (user) {
        context.familyProfile = {
          userName: user.firstName || 'User',
          dietaryPreferences: user.dietaryPreferences,
          familyMembers: user.familyMembers
        };
      }
    }

    // Include available meals for better context
    if (includeMeals) {
      const meals = await Meal.find().select('name category mealType dietaryTags').limit(50);
      context.availableMeals = meals;
    }

    const response = await geminiService.generateChatResponse(message, context);

    res.json({
      success: true,
      response,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating chat response:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate chat response',
      error: error.message
    });
  }
});

// Get available goals and health conditions
router.get('/goals', (req, res) => {
  res.json({
    success: true,
    goals: [
      {
        id: 'lose_weight',
        name: 'Lose Weight',
        description: 'Get meal suggestions to help with weight loss'
      },
      {
        id: 'build_muscle',
        name: 'Build Muscle',
        description: 'Get high-protein meals to support muscle building'
      },
      {
        id: 'manage_health',
        name: 'Manage a Health Condition',
        description: 'Get meals tailored to specific health conditions'
      },
      {
        id: 'eat_sustainably',
        name: 'Eat Sustainably',
        description: 'Get environmentally conscious meal suggestions'
      },
      {
        id: 'set_calorie_goal',
        name: 'Set Calorie Goal',
        description: 'Set your daily calorie target and get meal suggestions based on your goal'
      },
      {
        id: 'generate_meal_plan',
        name: 'Generate a meal plan',
        description: 'Create a personalized daily meal plan based on your dietary preferences'
      },
      {
        id: 'generate_weekly_plan',
        name: 'Generate a 7-day meal plan',
        description: 'Create a complete weekly meal plan for your family with variety and balance'
      }
    ],
    healthConditions: [
      {
        id: 'type2_diabetes',
        name: 'Type 2 Diabetes',
        description: 'Low-sugar, low-carb meal recommendations'
      },
      {
        id: 'celiac_disease',
        name: 'Celiac Disease',
        description: 'Gluten-free meal recommendations'
      },
      {
        id: 'hypertension',
        name: 'Hypertension',
        description: 'Low-sodium meal recommendations'
      },
      {
        id: 'heart_disease',
        name: 'Heart Disease',
        description: 'Heart-healthy, low-cholesterol meals'
      },
      {
        id: 'lactose_intolerance',
        name: 'Lactose Intolerance',
        description: 'Dairy-free meal recommendations'
      }
    ]
  });
});

// Analyze meals for family compatibility
router.post('/analyze-meals', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { meals, mealType, fastMode } = req.body;

    if (!meals || !Array.isArray(meals)) {
      return res.status(400).json({
        success: false,
        message: 'Meals array is required'
      });
    }

    // Get user profile with family members and name
    const user = await User.findById(userId).select('firstName lastName dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prepare family profile for analysis
    const familyProfile = {
      user: {
        name: user.firstName || 'You',
        dietaryPreferences: user.dietaryPreferences || {
          restrictions: [],
          allergies: [],
          dislikedIngredients: [],
          calorieTarget: 2000,
          mealFrequency: 3
        }
      },
      familyMembers: user.familyMembers || []
    };

    console.log('👨‍👩‍👧‍👦 Family Profile for Analysis:', JSON.stringify(familyProfile, null, 2));
    console.log('🍽️ Meals to Analyze:', meals.map(m => ({ name: m.name, id: m._id })));

    // Generate AI analysis for each meal
    const mealAnalysis = await geminiService.analyzeMealsForFamily(
      familyProfile,
      meals,
      mealType
    );

    res.json({
      success: true,
      analysis: mealAnalysis,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error analyzing meals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze meals',
      error: error.message
    });
  }
});

// Set calorie goal and get meal recommendations
router.post('/set-calorie-goal', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { calorieGoal } = req.body;

    if (!calorieGoal || calorieGoal < 1000 || calorieGoal > 5000) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid calorie goal between 1000 and 5000 calories'
      });
    }

    // Get user profile
    const user = await User.findById(userId).select('firstName lastName dietaryPreferences familyMembers');
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update user's calorie target
    if (!user.dietaryPreferences) {
      user.dietaryPreferences = {};
    }
    user.dietaryPreferences.calorieTarget = calorieGoal;
    await user.save();

    // Get all available meals from database
    const meals = await Meal.find();

    // Create user profile for AI recommendations
    const userProfile = {
      userName: user.firstName || 'User',
      dietaryPreferences: {
        ...user.dietaryPreferences,
        calorieTarget: calorieGoal
      },
      familyMembers: user.familyMembers || []
    };

    // Generate calorie-based meal recommendations
    const recommendations = await geminiService.generateCalorieBasedRecommendations(
      userProfile,
      meals,
      calorieGoal
    );

    res.json({
      success: true,
      calorieGoal: calorieGoal,
      recommendations: recommendations.recommendations,
      personalizedMessage: recommendations.personalizedMessage,
      nutritionalTips: recommendations.nutritionalTips,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error setting calorie goal:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set calorie goal and generate recommendations',
      error: error.message
    });
  }
});

// Test FAST meal analysis
router.get('/test-fast-analysis', async (req, res) => {
  try {
    const testFamilyProfile = {
      user: {
        name: 'You',
        dietaryPreferences: {
          restrictions: ['vegetarian'],
          allergies: ['nuts'],
          dislikedIngredients: [],
          calorieTarget: 2000,
          mealFrequency: 3
        }
      },
      familyMembers: [
        {
          name: 'John',
          dietaryPreferences: {
            restrictions: [],
            allergies: ['shellfish'],
            dislikedIngredients: []
          }
        }
      ]
    };

    const testMeals = [
      { _id: 'test1', name: 'Chicken Adobo', ingredients: ['chicken', 'soy sauce'], allergens: [], dietType: 'General', calories: 350 },
      { _id: 'test2', name: 'Vegetable Lumpia', ingredients: ['vegetables', 'wrapper'], allergens: [], dietType: 'Vegetarian', calories: 200 },
      { _id: 'test3', name: 'Beef Steak', ingredients: ['beef', 'onions'], allergens: [], dietType: 'General', calories: 400 },
      { _id: 'test4', name: 'Fish Sinigang', ingredients: ['fish', 'tamarind'], allergens: ['fish'], dietType: 'General', calories: 250 },
      { _id: 'test5', name: 'Tofu Sisig', ingredients: ['tofu', 'onions'], allergens: [], dietType: 'Vegetarian', calories: 180 }
    ];

    console.log('🚀 Testing 5-meal lazy loading analysis...');
    const startTime = Date.now();
    const result = await geminiService.analyzeMealsForFamily(testFamilyProfile, testMeals, 'lunch');
    const endTime = Date.now();
    const duration = endTime - startTime;

    res.json({
      success: true,
      message: `FAST analysis completed in ${duration}ms`,
      duration: `${duration}ms`,
      mealsAnalyzed: testMeals.length,
      result
    });
  } catch (error) {
    console.error('Test meal analysis failed:', error);
    res.status(500).json({
      success: false,
      message: 'Test meal analysis failed',
      error: error.message
    });
  }
});

// Test Gemini API connection
router.get('/test', async (req, res) => {
  try {
    const testResponse = await geminiService.generateContent('Say hello and confirm you are working properly.');

    res.json({
      success: true,
      message: 'Gemini AI service is working',
      testResponse
    });
  } catch (error) {
    console.error('Gemini API test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Gemini AI service test failed',
      error: error.message
    });
  }
});

module.exports = router;
