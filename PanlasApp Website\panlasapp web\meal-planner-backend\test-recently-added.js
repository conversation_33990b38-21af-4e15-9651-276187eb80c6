const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test meal data
const testMeal = {
  _id: '507f1f77bcf86cd799439011',
  name: '<PERSON><PERSON><PERSON>',
  mealType: ['lunch', 'dinner'],
  category: ['Filipino', 'Main Course'],
  dietaryTags: ['gluten-free'],
  rating: 4.5,
  calories: 350,
  protein: 25,
  carbs: 15,
  fat: 20,
  image: 'https://example.com/adobo.jpg',
  description: 'Classic Filipino chicken adobo with soy sauce and vinegar',
  ingredients: ['chicken', 'soy sauce', 'vinegar', 'garlic', 'bay leaves'],
  instructions: ['Marinate chicken', 'Cook in sauce', 'Simmer until tender'],
  prepTime: 45
};

async function testRecentlyAddedToMealPlans() {
  try {
    console.log('=== Testing Recently Added to Meal Plans API ===');
    
    // Step 1: Login to get token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    const token = loginResponse.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log('Login successful, token received');
    
    // Step 2: Get initial recently added to meal plans (should be empty or have existing data)
    console.log('2. Getting initial recently added to meal plans...');
    const initialResponse = await axios.get(`${API_BASE_URL}/users/recently-added-to-meal-plans`, { headers });
    console.log('Initial recently added to meal plans:', JSON.stringify(initialResponse.data, null, 2));
    
    // Step 3: Add a meal to recently added to meal plans
    console.log('3. Adding meal to recently added to meal plans...');
    const addResponse = await axios.post(`${API_BASE_URL}/users/recently-added-to-meal-plans`, 
      { 
        meal: testMeal,
        addedToDate: '2024-09-05',
        addedToMealType: 'lunch'
      }, 
      { headers }
    );
    console.log('Add meal response:', JSON.stringify(addResponse.data, null, 2));
    
    // Step 4: Add another meal with different data
    console.log('4. Adding second meal...');
    const testMeal2 = {
      ...testMeal,
      _id: '507f1f77bcf86cd799439012',
      name: 'Pancit Canton',
      calories: 280,
      image: 'https://example.com/pancit.jpg',
      description: 'Delicious Filipino stir-fried noodles'
    };
    
    const addResponse2 = await axios.post(`${API_BASE_URL}/users/recently-added-to-meal-plans`, 
      { 
        meal: testMeal2,
        addedToDate: '2024-09-06',
        addedToMealType: 'dinner'
      }, 
      { headers }
    );
    console.log('Add second meal response:', JSON.stringify(addResponse2.data, null, 2));
    
    // Step 5: Get recently added to meal plans again (should contain the meals)
    console.log('5. Getting recently added to meal plans after adding...');
    const finalResponse = await axios.get(`${API_BASE_URL}/users/recently-added-to-meal-plans`, { headers });
    console.log('Final recently added to meal plans:', JSON.stringify(finalResponse.data, null, 2));
    
    // Verify the meals were added
    const meals = finalResponse.data.recentlyAddedToMealPlans || [];
    if (meals.length > 0) {
      console.log('✓ SUCCESS: Meals were successfully added to recently added to meal plans!');
      console.log(`Found ${meals.length} meals in recently added to meal plans`);
      meals.forEach((meal, index) => {
        console.log(`Meal ${index + 1}: ${meal.name} - ${meal.calories} cal - Added to ${meal.addedToMealType} on ${meal.addedToDate}`);
      });
    } else {
      console.log('✗ FAILED: No meals found in recently added to meal plans');
    }
    
  } catch (error) {
    console.error('Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testRecentlyAddedToMealPlans();
